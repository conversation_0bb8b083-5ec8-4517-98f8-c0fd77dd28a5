public class Service_Product {
    public static Map<String, Product2> searchProductByLevelCode(Set<String> levelCodeSet){
        Map<String, Product2> levelCode_Product = new Map<String, Product2>();
        Map<String, Product2> productMap = new Map<String, Product2>([SELECT Id,Material_Type__c,ProductCode,
                        Name,QuantityUnitOfMeasure,
                        Material_Group__c,Family,LevelCode__c,Level__c,ParentProduct__c 
                        FROM Product2 
                        WHERE LevelCode__c IN :levelCodeSet
                        AND IsActive = true]);
        if (productMap.isEmpty()) {
            return null;
        }
        for (Product2 product : productMap.values()) {
            levelCode_Product.put(product.LevelCode__c, product);
        }
        return levelCode_Product;
    }
    //根据物料编码查询产品
    public static Map<String, Product2> searchProductByProductCode(Set<String> productCodeSet){
        Map<String, Product2> returnProduct = new Map<String, Product2>();
        Map<String, Product2> productMap = new Map<String, Product2>([SELECT Id,Material_Type__c,ProductCode,
                        Name,QuantityUnitOfMeasure,
                        Material_Group__c,Family,LevelCode__c,Level__c,ParentProduct__c 
                        FROM Product2 
                        WHERE ProductCode IN :productCodeSet
                        AND IsActive = true]);
        if (productMap.isEmpty()) {
            return returnProduct;
        }
        for (Product2 product : productMap.values()) {
            returnProduct.put(product.ProductCode, product);
        }
        return returnProduct;
    }

    public static void createPricebookEntries(List<Product2> newProducts) {
        // 获取标准价格手册ID
        Id stdPricebookId = getStandardPricebookId();
        
        if (stdPricebookId == null) {
            System.debug('未找到标准产品价格手册');
            return; // 如果没有找到标准价格手册，则退出
        }
        
        List<PricebookEntry> entriesToCreate = new List<PricebookEntry>();
        List<CurrencyType> currencies = [SELECT IsoCode FROM CurrencyType];
        for (Product2 prod : newProducts) {
            // 创建新的价格手册条目
            for(CurrencyType cobj: currencies) {
              
                PricebookEntry newEntry = new PricebookEntry(
                    Pricebook2Id = stdPricebookId,
                    Product2Id = prod.Id,
                    UnitPrice = 0.00, // 默认价格为0，可以根据业务需求调整
                    IsActive = true,
                    UseStandardPrice = false,
                    CurrencyIsoCode  = cobj.IsoCode
                );
                entriesToCreate.add(newEntry);
            }
        }
        
        if (!entriesToCreate.isEmpty()) {
            try {
                insert entriesToCreate;
            } catch (DmlException e) {
                // 处理异常，可以记录错误日志或发送通知
                System.debug('创建价格手册条目时出错: ' + e.getMessage());
            }
        }
    }
    
    // 获取标准价格手册ID
    private static Id getStandardPricebookId() {
        Pricebook2 stdPricebook = [SELECT Id FROM Pricebook2 WHERE IsStandard = true LIMIT 1];
        return stdPricebook.Id;
    }
}