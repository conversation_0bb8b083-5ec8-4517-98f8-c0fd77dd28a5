public without sharing class Payment_MasterLandPDFController {
    
    public Map<String, List<PaymentItem>> detailItems{ get; set; }
    public PaymentItem allItem{ get; set; }
    public PaymentHead headInfo{ get; set; }
    public Boolean hasItems{ get; set; }
    String recordId;
    List<BillPaymentAdviceDetail__c> details;
    public Payment_MasterLandPDFController() {
        // 从URL参数获取记录ID
        recordId = ApexPages.currentPage().getParameters().get('id');
        

        //结构数据

        
        headInfo = new PaymentHead();
        detailItems = new Map<String, List<PaymentItem>>();
        allItem = new PaymentItem();
        allItem.type =  '总项合计';
        // 查询相关数据
        if (String.isNotBlank(recordId)) {
            //付款通知书
            generateHeadInfo();
            hasItems = false;
            //付款通知书明细
            details = [SELECT Id,BillPaymentAdvice__c,
                            Product__c,Product__r.Name, Product__r.productCode, Product_Code__c, Unit__c,
                            Quantity__c, UnitPrice__c, UnitPrice_tax__c, 
                            Total_Amount__c,
                            Total_Amount_Tax__c, Tax__c, 
                            Tax_Rate__c, 
                            Area__c, 
                            PaymentType__c,  
                            ProductDetailProperty__c,
                            Configuration__c,
                            Service_StartDate__c,
                            Service_EndDate__c   
                        FROM BillPaymentAdviceDetail__c 
                        WHERE BillPaymentAdvice__c =:recordId 
                        AND Product__c != NULL 
                        ORDER BY Product__c,PaymentType__c DESC];
            if (!details.isEmpty()) {
                hasItems = true;
                generateItem();
            }
        }
    }

    public void generateItem(){
        

        //productCode,性质，付款计划明细
        Map<String, List<BillPaymentAdviceDetail__c>> returnItems = new Map<String,List<BillPaymentAdviceDetail__c>>();//行项目
        for (BillPaymentAdviceDetail__c detail : details) {
            if (returnItems.containsKey(detail.Product__r.productCode)) {
                List<BillPaymentAdviceDetail__c> tempItems  = returnItems.get(detail.Product__r.productCode);
                tempItems.add(detail);
                returnItems.put(detail.Product__r.productCode,tempItems);
            } else {
                List<BillPaymentAdviceDetail__c> tempItems = new List<BillPaymentAdviceDetail__c>();
                tempItems.add(detail);
                returnItems.put(detail.Product__r.productCode, tempItems);
            }
        }
        Decimal allTotalAmount = 0;
        Decimal allTotalAmount_Tax = 0;
        for (String productCode : returnItems.keySet()) {
            Decimal totalAmount = 0;
            Decimal totalAmount_Tax = 0;
            List<PaymentItem> tempItems  = new List<PaymentItem>();
            for (BillPaymentAdviceDetail__c detail : returnItems.get(productCode)) {
                PaymentItem item = new PaymentItem();
                item.productName = DataProcessTool.wrapLongData(detail.Product__r.Name,5);
                item.configuration = DataProcessTool.wrapLongData(detail.Configuration__c,5);
                item.productProperty = '';
                item.type = '实际量';
                item.paymentQuantity = DataProcessTool.formatNumberWithDigitLineBreaks(detail.Quantity__c,7);
                item.area = DataProcessTool.wrapLongData(String.valueOf( DataProcessTool.toLabel(detail,'Area__c')),5);
                item.startDate = DataProcessTool.formatStrWithDigitLineBreaks(String.valueOf(detail.Service_StartDate__c),5);
                item.endDate = DataProcessTool.formatStrWithDigitLineBreaks(String.valueOf(detail.Service_EndDate__c),5);
                item.unitPrice = DataProcessTool.formatNumberWithDigitLineBreaks(detail.UnitPrice__c,7);
                item.unitPrice_includeTax = DataProcessTool.formatNumberWithDigitLineBreaks(detail.UnitPrice_tax__c,7);
                item.price = DataProcessTool.formatNumberWithDigitLineBreaks(detail.Total_Amount__c,7);
                item.price_IncludeTax =DataProcessTool.formatNumberWithDigitLineBreaks(detail.Total_Amount_Tax__c,7);
                totalAmount+= detail.Total_Amount__c==null?0:detail.Total_Amount__c;
                totalAmount_Tax += detail.Total_Amount_Tax__c==null?0:detail.Total_Amount_Tax__c;
                tempItems.add(item);
            }
            PaymentItem itemAll = new PaymentItem();
            itemAll.type =  '本项合计';
            itemAll.price_IncludeTax = DataProcessTool.formatNumberWithDigitLineBreaks(totalAmount_Tax,7);
            itemAll.price = DataProcessTool.formatNumberWithDigitLineBreaks(totalAmount,7);
            tempItems.add(itemAll);
            detailItems.put(productCode, tempItems);

            //总项合计
            allTotalAmount += totalAmount;
            allTotalAmount_Tax += totalAmount_Tax;
            
        }
        allItem.price_IncludeTax = DataProcessTool.formatNumberWithDigitLineBreaks(allTotalAmount_Tax,7);
        allItem.price =  DataProcessTool.formatNumberWithDigitLineBreaks(allTotalAmount,7);
        
        
    }

    public void generateHeadInfo(){
        BillPaymentAdvice__c paymentRecord = [SELECT Id,Name,
                                                     RecordTypeId,
                                                     Account__c,Account__r.Name,
                                                     PartyB_Signing_Company__r.Name,
                                                     Contract_Number__c,
                                                     BankAccount__c,
                                                     AccountBankName__c,
                                                     StartDate__c,
                                                     EndDate__c,
                                                     ExpenseSettlement_Method__c, 
                                                     Contract_Currency__c,
                                                     PaymentCycle__c,
                                                     PlanNo__c,
                                                     PayableAmount__c,
                                                     Payment_Currency__c,
                                                     Exchange_Rate__c,
                                                     Due_Date__c,
                                                     CreatedDate,
                                                     Adjustment_Note__c
                                              FROM BillPaymentAdvice__c 
                                              WHERE Id = :recordId];
        
        headInfo.startDate =  paymentRecord.StartDate__c; //本期开始日期
        headInfo.endDate = paymentRecord.EndDate__c; //本期结束日期
        headInfo.paymentNo = paymentRecord.Name; //单据编号-付款通知书编号
        headInfo.accountName = paymentRecord.Account__r.Name;
        headInfo.accountBankName = paymentRecord.AccountBankName__c;  //开户行名称
        headInfo.bankAccount = paymentRecord.BankAccount__c; //银行账号
        headInfo.orderNo = paymentRecord.Contract_Number__c;  //订单编号-合同号
        headInfo.paymentType = DataProcessTool.getRecordTypeInfoById(paymentRecord.Id,paymentRecord.recordTypeId).get('Label');  //收入类型
        headInfo.contractCurrency = paymentRecord.Contract_Currency__c;  //合同币种
        headInfo.paymentMethod = DataProcessTool.toLabel(paymentRecord, 'ExpenseSettlement_Method__c');  //付款方式
        headInfo.paymentCycle = DataProcessTool.toLabel(paymentRecord, 'PaymentCycle__c');  //付款周期
        headInfo.paymentPeriod = paymentRecord.PlanNo__c;  //付款期数
        headInfo.payableAmount = paymentRecord.PayableAmount__c;  //应付金额
        headInfo.payment_Currency = paymentRecord.Payment_Currency__c; //结算币种
        headInfo.payment_exchangeRate = paymentRecord.Exchange_Rate__c; //结算汇率
        headInfo.due_date = paymentRecord.Due_Date__c; //应付款日期
        headInfo.adjustment_Note = DataProcessTool.wrapLongData(paymentRecord.Adjustment_Note__c,30);//调整备注
        headInfo.createDate = paymentRecord.CreatedDate.date();
        headInfo.partyB = paymentRecord.PartyB_Signing_Company__r.Name;
    }
    public class PaymentItem{
        public String productName{get; set;}
        public String configuration{get; set;}
        public String productProperty{get; set;}
        public String type{get; set;}
        public String paymentQuantity{get; set;}
        public String area{get; set;}
        public String startDate{get; set;}
        public String endDate{get; set;}
        public String unitPrice_includeTax{get; set;}
        public String unitPrice{get; set;}
        public String price_IncludeTax{get; set;}
        public String price{get; set;}
    }

    public class PaymentHead{
        public Date startDate{get; set;} //本期开始日期
        public Date endDate{get; set;}  //本期结束日期
        public String paymentNo{get; set;}  //单据编号-付款通知书编号
        public String accountName{get; set;} //客户名称
        
        public String accountBankName{get; set;}  //开户行名称
        public String bankAccount{get; set;} //银行账号
        public String orderNo{get; set;}  //订单编号-合同号
        public String paymentType{get; set;}  //收入类型
        public String contractCurrency{get; set;}  //合同币种
        public String paymentMethod{get; set;}  //付款方式
        public String paymentCycle{get; set;}  //付款周期
        public Decimal paymentPeriod{get; set;}  //付款期数
        public Decimal payableAmount{get; set;}  //应付金额
        public String payment_Currency{get; set;} //结算币种
        public Decimal payment_exchangeRate{get; set;} //结算汇率
        public Date due_date{get; set;} //应付款日期
        public String adjustment_Note{get; set;}//调整备注
        public Date createDate{get; set; }//付款通知书创建日期
        public String partyB{get; set; }
    }

}