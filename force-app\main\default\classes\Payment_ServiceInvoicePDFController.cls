public without sharing class Payment_ServiceInvoicePDFController {
    public List<PaymentItem> returnItems{ get; set; }  
    public BillFrom fromCom{get; set; }
    public String recordId{ get; set; }
    public Decimal totalAmount{ get; set; }
    public BillPaymentAdvice__c payment{get; set; }
    public List<BillPaymentAdviceDetail__c> details;
    public Boolean hasItems{ get; set; }
    public Payment_ServiceInvoicePDFController() {
        hasItems =false;
        // 从URL参数获取记录ID
        recordId = ApexPages.currentPage().getParameters().get('id');
        

        //结构数据

        returnItems = new List<PaymentItem>();//行项目
        fromCom = new BillFrom();
        // 查询相关数据
        if (String.isNotBlank(recordId)) {
            //TODO： 查询数据，构建展示数据
            payment = [SELECT Id,Name,
                                Account__c,Account__r.Name,Account__r.CompanyAddress__c,
                                Account__r.Customer_ICP_Number__c,
                                Account__r.Tax_Categroy__c,
                                Account__r.TAX_Num1__c,
                                PartyB_Signing_Company__c,
                                Contract_Number__c,
                                Billing_Number__c,
                                ExpenseSettlement_Method__c, 
                                Contract_Currency__c,
                                PaymentCycle__c,
                                PlanNo__c,
                                PayableAmount__c,
                                Payment_Currency__c,
                                Exchange_Rate__c,
                                Due_Date__c,
                                Adjustment_Note__c
                        FROM BillPaymentAdvice__c 
                        WHERE Id = :recordId];
            //乙方签约主体
            if (payment.PartyB_Signing_Company__c!=null) {
                generateFromCompany(payment.PartyB_Signing_Company__c);
            }
            //付款通知书明细
            details = [SELECT Id,BillPaymentAdvice__c,
                            Product__c,Product__r.Name, Product__r.productCode, Product_Code__c, Unit__c,
                            Quantity__c, UnitPrice__c, UnitPrice_tax__c, 
                            Total_Amount__c, 
                            Total_Amount_Tax__c, Tax__c, 
                            Tax_Rate__c, 
                            PaymentType__c,  
                            ProductDetailProperty__c,
                            Configuration__c,
                            Service_StartDate__c,
                            Service_EndDate__c   
                        FROM BillPaymentAdviceDetail__c 
                        WHERE BillPaymentAdvice__c =:recordId 
                        AND Product__c != NULL 
                        ORDER BY Product__c,PaymentType__c DESC];
            if (!details.isEmpty()) {
                hasItems = true;
                generateItem();
            }
        }
    }

    public void generateFromCompany(Id partyBId){
        //乙方签约主体信息
        Account acc = [SELECT Id, NAME, TAX_Num1__c,Tax_Categroy__c,CompanyAddress__c FROM Account WHERE Id = :partyBId];
        List<Contact> billContact = [SELECT Id,Name,Position__c,MobilePhone,AccountId,Contact_Type__c,Email FROM Contact WHERE Contact_Type__c ='账单' AND AccountId = :partyBId LIMIT 1];
        fromCom.company = DataProcessTool.wrapLongData(acc.Name,10);
        fromCom.companyAddress = DataProcessTool.wrapLongData(acc.CompanyAddress__c,10);
        fromCom.registrationNo = DataProcessTool.wrapLongData(acc.TAX_Num1__c,15);
        
        if (acc.Tax_Categroy__c == 'SG1') {
            //税分类 = GST注册号
            fromCom.GSTRegistrationNo = DataProcessTool.wrapLongData(acc.TAX_Num1__c,15);
        }
        if (!billContact.isEmpty()) {
            fromCom.contactPerson = billContact[0].Name;
            fromCom.position = billContact[0].Position__c;
            fromCom.tel = billContact[0].MobilePhone;
            fromCom.email = billContact[0].Email;
        }
        //乙方签约主体，银行信息
        List<BankInfo__c> bank = [SELECT Id,
                                        Account__c,
                                        Account__r.Name,
                                        Account_BankNumber__c,
                                        Account_Holder_Name__c,
                                        BranchBankName__r.Name,
                                        IBAN__c,BankAddress__c,
                                        Swift_Code__c 
                                FROM BankInfo__c 
                                WHERE Account__c = :partyBId LIMIT 1];
        if (!bank.isEmpty()) {
            fromCom.beneficiaryName = DataProcessTool.wrapLongData(bank[0].Account__r.Name,10);
            fromCom.beneficiaryAddress = DataProcessTool.wrapLongData(bank[0].BankAddress__c,10);
            fromCom.beneficiaryBankName = DataProcessTool.wrapLongData(bank[0].BranchBankName__r.Name,10);
            fromCom.beneficiaryACNo = DataProcessTool.wrapLongData(bank[0].Account_BankNumber__c,10);
            fromCom.SWIFT_Code = DataProcessTool.wrapLongData(bank[0].Swift_Code__c,15);
        }

        
        
    }
    public void generateItem(){
        totalAmount = 0;
        for (BillPaymentAdviceDetail__c detail : details) {
            PaymentItem item = new PaymentItem();
            item.productName = DataProcessTool.wrapLongData(detail.Product__r.Name,5);
            item.billingItems = '';
            item.rate = DataProcessTool.formatNumberWithDigitLineBreaks(detail.Tax_Rate__c,7);
            item.total = DataProcessTool.formatNumberWithDigitLineBreaks(detail.Total_Amount_Tax__c,7);
            item.startDate = DataProcessTool.formatStrWithDigitLineBreaks(String.valueOf(detail.Service_StartDate__c),8);
            item.endDate = DataProcessTool.formatStrWithDigitLineBreaks(String.valueOf(detail.Service_EndDate__c),8);
            if (detail.Total_Amount_Tax__c!=null) {
                totalAmount+=detail.Total_Amount_Tax__c;
            }
            returnItems.add(item);
        }
    }
    

    public class totalItem{
        public String totalAmount{ get; set; }
        public String GSTPersent{ get; set; }
        public String totalCurrentCharge{ get; set; }
    }
    
    public class PaymentItem{
        public String productName{get; set;}
        public String billingItems{get; set;}
        public String amount{get; set;}
        public String rate{get; set;}
        public String total{get; set;}
        public String startDate{get; set;}
        public String endDate{get; set;}
    }

    public class BillFrom{
        public String company{get; set;} 
        public String companyAddress{get; set;} 
        public String registrationNo{get; set;} 
        public String GSTRegistrationNo{ get; set;}
        public String contactPerson{ get; set;}  
        public String position{ get; set;} 
        public String tel{ get; set;}  
        public String email{ get; set;}  

        public String beneficiaryName{get; set;}
        public String beneficiaryBankName{get; set;}
        public String beneficiaryAddress{get; set;}
        public String beneficiaryACNo{get; set;}
        public String SWIFT_Code{get; set;}
    }
    public class BillTo{

    }
}