public without sharing class Interface_CRMSyncContractToSAP {
    public Interface_OutboundParam param {get;set;}
    Map<String, Object> requestBody = new Map<String, Object>();

    public Interface_CRMSyncContractToSAP(Id contractId) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncContractToSAP';
        param.endpoint ='callout:SAP_API_Credential';
        param.retryTimes = 3;
        param.recordIdSet = new Set<id>{contractId};
        param.targetObject = 'Contract';
        
        //String token = '';
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(contractId);

        Interface_OutboundExecutor syncContract = new Interface_OutboundExecutor(param);
        String responseBody = syncContract.execute();
        // 解析SAP接口返回的数据
        if (String.isNotBlank(responseBody)) {
            try {
                // 假设返回为JSON格式
                Map<String, Object> respMap = (Map<String, Object>) JSON.deserializeUntyped(responseBody);
                String msgType = (respMap.containsKey('msgty') && respMap.get('msgty') != null) ? String.valueOf(respMap.get('msgty')) : '';
                String msgText = (respMap.containsKey('msgtx') && respMap.get('msgtx') != null) ? String.valueOf(respMap.get('msgtx')) : '';
                String vbeln = (respMap.containsKey('sapnum') && respMap.get('sapnum') != null) ? String.valueOf(respMap.get('sapnum')) : '';

                if(msgType == 'S') {
                    Contract con = new Contract();
                    con.Id = contractId;
                    con.SapContractNo__c = vbeln;
                    update con;
                }else if(msgType == 'E') {
                    
                }

            } catch (Exception e) {
                throw new IllegalArgumentException('解析失败');
            }
        }

        
    }

    public String getRequestHeader(){
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        //headerMap.put('Authorization', 'Bearer '+token);
        //headerMap.put('Authorization', 'Basic ' + encodedCredentials);
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    public String getRequestBody(Id contractId){
        List<Contract> contractList = [
            SELECT Id,SalesOrganization__c,Account.SalesGroup__c,Product_Category__c, Account.SAP_Num__c,ContractAutoNo__c,Createddate,DistributionChannel__c,ContractType__c,
            Service_Start__c,Service_End__c,PaymentTerm__c,Contract_Status__c,ContractCurrency__c,SettlementCurrency__c,SubscribeNewProducts__c,
            ExchangeRateIdentification__c,Non_Standard_Rate__c,Owner.Name,Quote__r.QuoteNumber,OriginContract__r.ContractAutoNo__c,
            (Select id,ContractLineItemNo__c,CustomerIDs__c,MaterialSAPNumber__c,Quantity__c,Unit__c,NetPrice__c,TaxCode_formula__c,
            PriceUnit__c,TotalPrice__c,ValidFrom__c,ValidTo__c,ProductArea__c,Quote_Line_Item__c,Product_Group__r.Name,ISGROUP__c from Contract_Products__r) FROM Contract WHERE Id =:contractId];

        // List<CollectionPlanLine__c> planList = [SELECT id,Description__c,Period_StartDate__c,Period_EndDate__c,(Select id,Product__r.ProductCode,EndDate__c,DaysCount__c From CollectionPlanLines__r) FROM CollectionPlanLine__c WHERE CollectionPlan__r.Contract__c =:contractId];
        // // 将planList转成json串文本
        // String planListJson = JSON.serialize(planList);
        String planListJson = CWUtility.getPlanJosn(contractId);
        //获取报价方式上的数据
        Set<id> quoteItemIds = new Set<id>();
        for(Contract obj:contractList){
            for(Contract_Product__c prod:obj.Contract_Products__r){
                quoteItemIds.add(prod.Quote_Line_Item__c);
            }
        }
        Map<id,QuoteItemInfo> quoteMethodMap = new Map<id,QuoteItemInfo>();
        for(Quotation_Method__c qm:[SELECT id,Method__c,Discount_Factor__c,Quote_Line_Item_ID__c,Fixed_UnitPrice__c FROM Quotation_Method__c WHERE Quote_Line_Item_ID__c IN:quoteItemIds]){
            QuoteItemInfo qmi = new QuoteItemInfo();
            qmi.quoteItemId = qm.Quote_Line_Item_ID__c == null ?'':qm.Quote_Line_Item_ID__c;
            qmi.productDiscount = qm.Discount_Factor__c == null?'':String.valueOf(qm.Discount_Factor__c);
            qmi.quoteMethod = qm.Method__c == null?'':qm.Method__c;
            quoteMethodMap.put(qm.Quote_Line_Item_ID__c,qmi);
        }

        
        RequestBody body = new RequestBody();
        for(Contract con : contractList){
            ContractRequest cr = new ContractRequest();
            cr.AUART = 'Z001'; //con.ContractType__c==null ? '':con.ContractType__c;
            cr.VKORG = con.SalesOrganization__c == null ?'':con.SalesOrganization__c;
            cr.VTWEG = con.DistributionChannel__c == null ?'':con.DistributionChannel__c;
            cr.SPART = con.Product_Category__c == null ? '':con.Product_Category__c;
            cr.KUNNR = con.Account.SAP_Num__c == null ? '' : con.Account.SAP_Num__c;
            cr.BSTKD = con.ContractAutoNo__c == null ? '' : con.ContractAutoNo__c;
            cr.AUDAT = con.Createddate == null ? '' : DataProcessTool.formatSAPDate(Date.valueOf(con.Createddate));
            cr.GUEBG = con.Service_Start__c == null ? '' : DataProcessTool.formatSAPDate(con.Service_Start__c);
            cr.GUEEN = con.Service_End__c == null ? '' : DataProcessTool.formatSAPDate(con.Service_End__c);
            cr.ZTERM = con.PaymentTerm__c == null ? '' : con.PaymentTerm__c;
            cr.ZCONCUR = con.ContractCurrency__c == null ? '' : con.ContractCurrency__c;
            cr.ZSETCUR = con.SettlementCurrency__c == null ? '' : con.SettlementCurrency__c;
            cr.ZERIDENT = con.ExchangeRateIdentification__c == null ? '' : con.ExchangeRateIdentification__c;
            cr.ZEXRATE = con.Non_Standard_Rate__c == null ? '' : String.valueOf(con.Non_Standard_Rate__c);
            cr.ZSALPER = con.Owner.Name == null ? '' : con.Owner.Name;
            cr.ZORCON = con.OriginContract__r.ContractAutoNo__c == null ? '' : con.OriginContract__r.ContractAutoNo__c;
            cr.ZQUNUM = con.Quote__r.QuoteNumber  == null ? '' : con.Quote__r.QuoteNumber;
            cr.ZUPDPRO = con.SubscribeNewProducts__c ? 'X':'';
            cr.ZTEXT = planListJson;
            body.req = cr;
            for(Contract_Product__c cp : con.Contract_Products__r){
                ContractItem item = new ContractItem();
                item.ZITEMS = cp.Id;
                item.POSNR = cp.ContractLineItemNo__c == null ? '':cp.ContractLineItemNo__c;
                item.ZTEXT1 = cp.CustomerIDs__c == null ? '':cp.CustomerIDs__c;
                item.MATNR = cp.MaterialSAPNumber__c == null ? '':cp.MaterialSAPNumber__c;
                item.ZMENG = cp.Quantity__c == null ? '':String.valueOf(cp.Quantity__c);
                item.ZIEME = cp.Unit__c == null ? '':cp.Unit__c;
                item.KPEIN = cp.PriceUnit__c  == null ? '':String.valueOf(cp.PriceUnit__c);
                item.NETPR = cp.NetPrice__c == null ? '':String.valueOf(cp.NetPrice__c);
                item.MWSK1 = cp.TaxCode_formula__c == null ? '':cp.TaxCode_formula__c;
                item.ZVLDF = cp.ValidFrom__c == null ? '':DataProcessTool.formatSAPDate(cp.ValidFrom__c);
                item.ZVLDT = cp.ValidTo__c == null ? '':DataProcessTool.formatSAPDate(cp.ValidTo__c);
                item.ZAREA = cp.ProductArea__c == null ? '':cp.ProductArea__c;
                if( quoteMethodMap.containsKey(cp.Quote_Line_Item__c)){
                    QuoteItemInfo qmi = quoteMethodMap.get(cp.Quote_Line_Item__c);
                    item.ZDISCOUNT = qmi.productDiscount == null ? '':String.valueOf(qmi.productDiscount);
                    item.ZQUOTYPE = qmi.quoteMethod == null ? '':qmi.quoteMethod;
                    item.ZGXZHBS = cp.ISGROUP__c ? 'X':'';
                    item.ZSHGRO = cp.Product_Group__r.Name == null ? '':cp.Product_Group__r.Name;
                }else{
                    item.ZDISCOUNT ='';
                    item.ZQUOTYPE = '';
                    item.ZGXZHBS ='';
                    item.ZSHGRO ='';
                }
                
                cr.item.add(item);
            }
        }
        String reqString = JSON.serialize(body);
        system.debug(reqString);
        return reqString;
    }

    public class RequestBody{
        public String UUID;//接口唯一标识
        public String ZNUMB;//接口编号
        public String FSYSID;//请求系统
        public ContractRequest req;
        public RequestBody(){
            UUID = CWUtility.generateUUID();
            ZNUMB = 'SD009';
            FSYSID = 'SALESFORCE';
            req = new ContractRequest();
        }
    }

    public class ContractRequest {
        public String AUART;//合同类型
        public String VKORG;//销售组织
        public String VTWEG;//分销渠道
        public String SPART;//产品组
        public String KUNNR;//客户编码
        public String BSTKD;//合同参考
        public String AUDAT;//凭证日期
        public String GUEBG;//有效期自
        public String GUEEN;//有效期至
        public String ZTERM;//付款条件
        public String ZCONCUR;//合同币种
        public String ZSETCUR;//结算币种
        public String ZERIDENT;//汇率标识
        public String ZEXRATE;//汇率
        public String ZSALPER;//销售员
        public String ZORCON ;//原SF合同号
        public String ZQUNUM;//SF报价单号
        public String ZUPDPRO;//是否自动增加新产品
        public String ZTEXT;//收款计划
        
        public List<ContractItem> item;
        public ContractRequest(){
            item = new List<ContractItem>();
        }
    } 

    public class ContractItem {
        public String ZITEMS;//ID
        public String POSNR;//合同行项目
        public String ZTEXT1;//客户ID
        public String MATNR;//物料编码
        public String ZMENG;//数量
        public String ZIEME;//计量单位
        public String NETPR;//净价
        public String KPEIN;//价格单位
        public String MWSK1;//税码
        //public String NETWR;//合同总金额
        public String ZVLDF;//有效期自
        public String ZVLDT;//有效期至
        public String ZAREA;//产品区域
        public String ZDISCOUNT;//产品折扣
        public String ZQUOTYPE;//报价类型
        public String ZGXZHBS;//共享标识
        public String ZSHGRO;//共享组
    }

    public class QuoteItemInfo{
        public String quoteItemId;
        public String productDiscount;
        public String quoteMethod;
        public String isGroup;
        public String groupName;
        public String quoteItemPrice;
        public String quoteItemQuantity;
    }


}