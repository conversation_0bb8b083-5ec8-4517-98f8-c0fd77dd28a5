public with sharing class DataProcessTool {
    //格式化日期，输入20250712，输出日期格式
    public static Date parseSAPDate(String sapDate) {
        if (sapDate == null || sapDate == '99991231') {
            return null;
        }
        Integer year = Integer.valueOf(sapDate.substring(0, 4));
        Integer month = Integer.valueOf(sapDate.substring(4, 6));
        Integer day = Integer.valueOf(sapDate.substring(6, 8));
        return Date.newInstance(year, month, day);
    }

    public static String formatSAPDate(Date inputDate) {
        if (inputDate == null) {
            return '';
        }
        // 将日期格式 2025-07-24 转换为 20250724
        String yearStr = String.valueOf(inputDate.year());
        String monthStr = String.valueOf(inputDate.month());
        if (monthStr.length() == 1) monthStr = '0' + monthStr;
        String dayStr = String.valueOf(inputDate.day());
        if (dayStr.length() == 1) dayStr = '0' + dayStr;
        return yearStr + monthStr + dayStr;
    }

    //长字符串根据长度换行
    public static String wrapLongData(String text, Integer lineLength){
        if (text == null) return '';
    
        // 中文等不需要空格分隔的语言
        Pattern chinesePattern = Pattern.compile('[\\u4e00-\\u9fa5]');
        Matcher matcher = chinesePattern.matcher(text);
        if (matcher.find()) {
            return text.replaceAll('(.{' + lineLength + '})', '$1\n');
        }
        // 英文等需要按单词换行的语言
        else {
            return wrapByWords(text, lineLength);
        }
    }
    
    public static String wrapByWords(String text, Integer maxLineLength) {
        if (text == null || text.length() <= maxLineLength) {
            return text;
        }
        
        Pattern p = Pattern.compile('.{1,' + maxLineLength + '}(\\s+|$)|\\S+?(\\s+|$)');
        Matcher m = p.matcher(text);
        List<String> lines = new List<String>();
        
        while (m.find()) {
            lines.add(m.group().trim());
        }
        String returnStr = String.join(lines, '\n');
        System.debug(returnStr);
        return returnStr;
    }

    //数字自动换行
    public static String formatNumberWithDigitLineBreaks(Decimal inputNumber,Integer maxLineLength) {
        if (inputNumber==null) {
            return null;
        }
        String numStr = String.valueOf(inputNumber);
        // 从右向左每5位添加一个换行符
        return numStr.replaceAll('(.{'+maxLineLength+'})(?!$)', '$1\n');
    }
    //数字自动换行
    public static String formatStrWithDigitLineBreaks(String inputStr,Integer maxLineLength) {
        if (inputStr==null) {
            return null;
        }
        String numStr = String.valueOf(inputStr);
        // 从右向左每5位添加一个换行符
        return numStr.replaceAll('(.{'+maxLineLength+'})(?!$)', '$1\n');
    }

    //根据记录的字段值，获取picklist的label
    public static String toLabel(SObject record, String fieldName) {
        if (record == null || String.isBlank(fieldName)) return '';
        
        String fieldValue = (String)record.get(fieldName);
        if (String.isBlank(fieldValue)) return '';
        
        Schema.DescribeFieldResult fieldDescribe = record.getSObjectType()
                                                 .getDescribe()
                                                 .fields.getMap()
                                                 .get(fieldName)
                                                 .getDescribe();
        
        for (Schema.PicklistEntry entry : fieldDescribe.getPicklistValues()) {
            if (entry.getValue() == fieldValue) {
                return entry.getLabel();
            }
        }
        return fieldValue;
    }
   // 根据recordId获取DeveloperName和Label
    public static Map<String, String> getRecordTypeInfoById(Id recordId, Id recordTypeId) {
        Map<String, String> result = new Map<String, String>{
            'DeveloperName' => null,
            'Label' => null
        };
        
        if (recordTypeId == null) return result;
        
        // 获取记录类型的SObject类型
        Schema.SObjectType sObjectType = recordId.getSobjectType();
        
        // 获取记录类型信息
        Schema.RecordTypeInfo recordTypeInfo = sObjectType.getDescribe().getRecordTypeInfosById().get(recordTypeId);
        System.debug('recordTypeInfo'+recordTypeInfo);
        
        if (recordTypeInfo != null) {
            result.put('DeveloperName', recordTypeInfo.getDeveloperName());
            result.put('Label', recordTypeInfo.getName()); // getName()返回的是Label
        }
    
        return result;
    }
}