public with sharing class CollectionPlan<PERSON>ine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Trigger<PERSON>andler {
    public static String DepositPaymentAdvice_RECORDTYPEID;
    public static String ContractAmendmentPaymentNotice_RECORDTYPEID;
    public static String ClosedContractPaymentNotice_RECORDTYPEID;
    public static String PrepaymentAdvice_RECORDTYPEID;

    public CollectionPlanLineTriggerHandler() {
        super('CollectionPlanLine__c');
        if(DepositPaymentAdvice_RECORDTYPEID == null)DepositPaymentAdvice_RECORDTYPEID = Schema.SObjectType.BillPaymentAdvice__c.getRecordTypeInfosByDeveloperName().get('DepositPaymentAdvice').getRecordTypeId();
        if(ContractAmendmentPaymentNotice_RECORDTYPEID == null)ContractAmendmentPaymentNotice_RECORDTYPEID = Schema.SObjectType.BillPaymentAdvice__c.getRecordTypeInfosByDeveloperName().get('ContractAmendmentPaymentNotice').getRecordTypeId();
        if(ClosedContractPaymentNotice_RECORDTYPEID == null)ClosedContractPaymentNotice_RECORDTYPEID = Schema.SObjectType.BillPaymentAdvice__c.getRecordTypeInfosByDeveloperName().get('ClosedContractPaymentNotice').getRecordTypeId();
        if(PrepaymentAdvice_RECORDTYPEID == null)PrepaymentAdvice_RECORDTYPEID = Schema.SObjectType.BillPaymentAdvice__c.getRecordTypeInfosByDeveloperName().get('PrepaymentAdvice').getRecordTypeId();
    }

    public override void doAfterInsert(List<SObject> newList){
        List<CollectionPlanLine__c> newRequests = (List<CollectionPlanLine__c>)newList;
        createPaymentNotice(newRequests);
    }

    public void createPaymentNotice(List<CollectionPlanLine__c> newRequests){
        Set<id> collectionPlanIds = new Set<id>();
        for(CollectionPlanLine__c line : newRequests){
            collectionPlanIds.add(line.CollectionPlan__c);   
        }
        List<CollectionPlan__c> collectionPlans = [
            SELECT Id,Contract__r.PartyB_Signing_Company__c,Contract__r.SalesOrganization__c,Contract__r.Non_Standard_Rate__c,PaymentCycle__c,Contract__r.ContractCurrency__c,Contract__r.SettlementCurrency__c,Contract__r.SapContractNo__c,
            Contract__r.ContractAutoNo__c,Contract__r.AccountId,Contract__r.Account.SAP_Num__c,Expense_Settlement_Method__c,
            (SELECT id,Payment_Notice_Amt__c,Period_StartDate__c,Period_EndDate__c,PaymentDate__c,isDeposit__c,isPrepay__c,isPreActived__c,isInstallment__c
            FROM CollectionPlans__r 
            WHERE (isDeposit__c = true OR isPrepay__c = true ) AND alreadySendBilling__c = false) //OR isPreActived__c = true
            FROM CollectionPlan__c WHERE Id IN :collectionPlanIds];
        List<BillPaymentAdvice__c> paymentAdvices = new List<BillPaymentAdvice__c>();
        for(CollectionPlan__c plan : collectionPlans){
            if(plan.CollectionPlans__r == null || plan.CollectionPlans__r.size() == 0) continue;
                for(CollectionPlanLine__c line:plan.CollectionPlans__r){
                    BillPaymentAdvice__c bpa = new BillPaymentAdvice__c();
                    bpa.Account__c = plan.Contract__r.AccountId;
                    bpa.Contract_Number__c = plan.Contract__r.ContractAutoNo__c;  
                    bpa.SAP_Contract_Number__c = plan.Contract__r.SapContractNo__c;
                    bpa.Contract_Currency__c =  plan.Contract__r.ContractCurrency__c;
                    bpa.Payment_Currency__c = plan.Contract__r.SettlementCurrency__c;
                    bpa.ExpenseSettlement_Method__c = plan.Expense_Settlement_Method__c;
                    bpa.PaymentCycle__c = plan.PaymentCycle__c;
                    bpa.PayableAmount__c =line.Payment_Notice_Amt__c;
                    bpa.Total_Amount__c = line.Payment_Notice_Amt__c;
                    bpa.Exchange_Rate__c = plan.Contract__r.Non_Standard_Rate__c ==null?0: Decimal.valueOf(plan.Contract__r.Non_Standard_Rate__c);
                    // bpa.EndDate__c = line.Period_EndDate__c;
                    // bpa.StartDate__c = line.Period_StartDate__c;
                    // bpa.Due_Date__c = line.PaymentDate__c;
                    bpa.Collection_Plan_Line__c = line.Id;
                    bpa.Creat_Date__c = line.Period_EndDate__c;
                    bpa.Company_Code__c = plan.Contract__r.SalesOrganization__c;
                    bpa.PartyB_Signing_Company__c = plan.Contract__r.PartyB_Signing_Company__c;
                    bpa.UniqueKey__c = bpa.Contract_Number__c  +'-'+  DataProcessTool.formatSAPDate(bpa.Creat_Date__c);
                    if(line.isDeposit__c) bpa.RecordTypeId = DepositPaymentAdvice_RECORDTYPEID;
                    if(line.isPrepay__c) bpa.RecordTypeId = PrepaymentAdvice_RECORDTYPEID;
                    if(line.isPreActived__c) bpa.RecordTypeId = ContractAmendmentPaymentNotice_RECORDTYPEID;
                    if(plan.Expense_Settlement_Method__c == 'Installment​') bpa.RecordTypeId = ClosedContractPaymentNotice_RECORDTYPEID; 
                    paymentAdvices.add(bpa);
                }
        } 
            if(paymentAdvices.size() > 0){
                insert paymentAdvices;
            }      
    }
        
}