public with sharing class Opportunity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Tri<PERSON><PERSON>andler {
    public OpportunityTriggerHandler() {
        super('Opportunity');
    }
    
    public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Opportunity> newOpportunities = (List<Opportunity>)newList;
        Map<Id, Opportunity> oldOpportunities = (Map<Id, Opportunity>)oldMap;
        
        // 处理审批通过的机会，创建销售状态记录并同步账户到SAP
        handleApprovedOpportunities(newOpportunities, oldOpportunities);
        handleLostOpportunities(newOpportunities, oldOpportunities);

       handleApprovalStatusChangeForAccountLock(newOpportunities, oldOpportunities);
    }

    public override void doBeforeUpdate(List<SObject> newList, Map<Id, SObject> oldMap){
        //提交商机报备审批的时候，客户和商机信息必填
        System.debug('2');
        checkApprovalInfo((List<Opportunity>)newList, (Map<Id, Opportunity>)oldMap);
    }

    // ================== override方法结束，自定义方法开始 ==================
    private void handleLostOpportunities(List<Opportunity> newOpportunities, Map<Id, Opportunity> oldOpportunities) {
        Set<Id> accountIdsToProcess = new Set<Id>();
        Map<Id, Opportunity> approvedOpportunities = new Map<Id, Opportunity>();
        for(Opportunity newOppo : newOpportunities) {
            Opportunity oldOppo = oldOpportunities.get(newOppo.Id);
            System.debug(newOppo.StageName+'----'+ oldOppo.StageName + '---'+newOppo.Is_Test__c);
            if(newOppo.StageName != oldOppo.StageName && newOppo.StageName == 'Closed Lost') {
                accountIdsToProcess.add(newOppo.AccountId);
                approvedOpportunities.put(newOppo.Id, newOppo);
            }
        }
        System.debug('accountIdsToProcess'+ accountIdsToProcess.size());
        if(accountIdsToProcess.isEmpty()) {
            return;
        }
        
        // 查询相关客户
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, SAP_Num__c, 
            (SELECT Id, Product_Line__c, Sales_Status__c FROM ChildAccounts__r LIMIT 1) 
            FROM Account 
            WHERE Id IN :accountIdsToProcess
        ]);
        List<Sales_Status__c> salesStatusToInsert = new List<Sales_Status__c>();
        List<Sales_Status__c> salesStatusToUpdate = new List<Sales_Status__c>();
        Map<Id,String> createAccountIdMap = new Map<Id,String>();
        // Map<Id,String> updateAccountIds = new Map<Id,String>();
        String productLine;
        
        // 准备批量插入的销售状态记录和SAP同步数据
        System.debug('approvedOpportunities***:'+approvedOpportunities.values().size());
        for(Opportunity opportunity : approvedOpportunities.values()) {
            Account account = accountMap.get(opportunity.AccountId);
            productLine = opportunity.Primary_Product__c;
            
            if(account != null && account.ChildAccounts__r.size() == 0) {
                // 准备销售状态记录
                Sales_Status__c salesStatus = new Sales_Status__c();
                salesStatus.Account__c = opportunity.AccountId;
                salesStatus.Product_Line__c = opportunity.Primary_Product__c;
                salesStatus.Sales_Status__c = '99';
                salesStatus.SalesPerson__c = opportunity.SalesPerson__c;
                salesStatus.Product_Group_Control__c = opportunity.Product_Group_Control__c;
                salesStatusToInsert.add(salesStatus);
                
                // 存储产品线信息，用于SAP同步
                
                
                // 根据SAP_Num__c判断同步类型
                // if(String.isEmpty(account.SAP_Num__c)) {
                createAccountIdMap.put(account.Id, productLine);
                // } else {
                //     updateAccountIds.put(account.Id, productLine);
                // } 
            }else if(account.ChildAccounts__r.size() > 0){
                Boolean contains = false;
                for(Sales_Status__c salesStatus : account.ChildAccounts__r) {
                    System.debug(salesStatus.Product_Line__c + ' ----' + productLine + ' ----' + salesStatus.Sales_Status__c + ' ---' );
                    if(salesStatus.Product_Line__c == opportunity.Primary_Product__c) {
                        if(salesStatus.Sales_Status__c == '01'){
                            salesStatus.Sales_Status__c = '99';
                            salesStatusToUpdate.add(salesStatus);
                            createAccountIdMap.put(account.Id, productLine);
                        }
                        contains = true;
                        
                    }
                }
                if(contains == false) {
                    Sales_Status__c salesStatus = new Sales_Status__c();
                    salesStatus.Account__c = opportunity.AccountId;
                    salesStatus.Product_Line__c = opportunity.Primary_Product__c;
                    salesStatus.Sales_Status__c = '99';
                    salesStatus.SalesPerson__c = opportunity.SalesPerson__c;
                    salesStatus.Product_Group_Control__c = opportunity.Product_Group_Control__c;
                    salesStatusToInsert.add(salesStatus);     
                    createAccountIdMap.put(account.Id, productLine);               
                }
            }
        }
        // 批量更新销售状态记录
        if(!salesStatusToUpdate.isEmpty()) {
            update salesStatusToUpdate;
        }
        // 批量插入销售状态记录
        System.debug('salesStatusToInsert***:'+salesStatusToInsert.size());
        if(!salesStatusToInsert.isEmpty()) {
            insert salesStatusToInsert;
        }
        
        // 批量同步到SAP
        System.debug('createAccountIdMap***'+createAccountIdMap.keySet().size());
        if(createAccountIdMap.keySet().size()>0) {
            Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(createAccountIdMap);
        }
    }
    /**
     * 处理审批通过的机会
     * 1. 对于没有销售状态记录的客户，创建销售状态记录
     * 2. 根据客户SAP_Num__c字段是否为空，同步客户信息到SAP
     */
    private void handleApprovedOpportunities(List<Opportunity> newOpportunities, Map<Id, Opportunity> oldOpportunities) {
        Set<Id> accountIdsToProcess = new Set<Id>();
        Map<Id, Opportunity> approvedOpportunities = new Map<Id, Opportunity>();
        
        // 筛选出状态变为已审批的机会
        for(Opportunity newOppo : newOpportunities) {
            Opportunity oldOppo = oldOpportunities.get(newOppo.Id);
            System.debug(newOppo.ApprovalStatus__c+'----'+ oldOppo.ApprovalStatus__c + '---'+newOppo.Is_Test__c);
            if(newOppo.ApprovalStatus__c != oldOppo.ApprovalStatus__c && newOppo.ApprovalStatus__c == 'Approved' && newOppo.Is_Test__c) {
                accountIdsToProcess.add(newOppo.AccountId);
                approvedOpportunities.put(newOppo.Id, newOppo);
            }
        }
        System.debug('accountIdsToProcess'+ accountIdsToProcess.size());
        if(accountIdsToProcess.isEmpty()) {
            return;
        }
        
        // 查询相关客户
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, SAP_Num__c, 
            (SELECT Id, Product_Line__c, Sales_Status__c FROM ChildAccounts__r LIMIT 1) 
            FROM Account 
            WHERE Id IN :accountIdsToProcess
        ]);
        
        // 批量处理销售状态记录创建和SAP同步
        createSalesStatusRecordsAndSyncToSAP(approvedOpportunities, accountMap);
         
    }
    
    /**
     * 批量创建销售状态记录并同步到SAP
     */
    private void createSalesStatusRecordsAndSyncToSAP(Map<Id, Opportunity> approvedOpportunities, Map<Id, Account> accountMap) {
        List<Sales_Status__c> salesStatusToInsert = new List<Sales_Status__c>();
        Map<Id,String> createAccountIdMap = new Map<Id,String>();
        // Map<Id,String> updateAccountIds = new Map<Id,String>();
        String productLine;
        
        // 准备批量插入的销售状态记录和SAP同步数据
        System.debug('approvedOpportunities***:'+approvedOpportunities.values().size());
        for(Opportunity opportunity : approvedOpportunities.values()) {
            Account account = accountMap.get(opportunity.AccountId);
            
            if(account != null && account.ChildAccounts__r.size() == 0) {
                // 准备销售状态记录
                Sales_Status__c salesStatus = new Sales_Status__c();
                salesStatus.Account__c = opportunity.AccountId;
                salesStatus.Product_Line__c = opportunity.Primary_Product__c;
                salesStatus.Sales_Status__c = '01';
                salesStatus.SalesPerson__c = opportunity.SalesPerson__c;
                salesStatus.Product_Group_Control__c = opportunity.Product_Group_Control__c;
                salesStatusToInsert.add(salesStatus);
                
                // 存储产品线信息，用于SAP同步
                productLine = opportunity.Primary_Product__c;
                
                // 根据SAP_Num__c判断同步类型
                // if(String.isEmpty(account.SAP_Num__c)) {
                    createAccountIdMap.put(account.Id, productLine);
                // } else {
                //     updateAccountIds.put(account.Id, productLine);
                // } 
            }else if(account.ChildAccounts__r.size() > 0){
                Boolean contains = false;
                for(Sales_Status__c salesStatus : account.ChildAccounts__r) {
                    System.debug(salesStatus.Product_Line__c + ' ----' + productLine + ' ----' + salesStatus.Sales_Status__c + ' ---' );
                    if(salesStatus.Product_Line__c == opportunity.Primary_Product__c) {
                        contains = true;
                    }
                }
                if(contains == false) {
                    Sales_Status__c salesStatus = new Sales_Status__c();
                    salesStatus.Account__c = opportunity.AccountId;
                    salesStatus.Product_Line__c = opportunity.Primary_Product__c;
                    salesStatus.Sales_Status__c = '01';
                    salesStatus.SalesPerson__c = opportunity.SalesPerson__c;
                    salesStatus.Product_Group_Control__c = opportunity.Product_Group_Control__c;
                    salesStatusToInsert.add(salesStatus);     
                    createAccountIdMap.put(account.Id, productLine);               
                }
            }
        }
        
        // 批量插入销售状态记录
        System.debug('salesStatusToInsert***:'+salesStatusToInsert.size());
        if(!salesStatusToInsert.isEmpty()) {
            insert salesStatusToInsert;
        }
        
        // 批量同步到SAP
        System.debug('createAccountIdMap***'+createAccountIdMap.keySet().size());
        if(createAccountIdMap.keySet().size()>0) {
            Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(createAccountIdMap);
        }
        
        // if(updateAccountIds.keySet().size()>0) {
        //     Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(updateAccountIds, 'update');
        // }
    }

    public static void checkApprovalInfo(List<Opportunity> newList, Map<Id, Opportunity> oldMap){
        List<Opportunity> needCheckList = new List<Opportunity>();
        for (Opportunity opp : newList) {
            Opportunity old = oldMap.get(opp.Id);
            if (opp.ApprovalStatus__c != old.ApprovalStatus__c 
                && opp.ApprovalStatus__c == 'Submitted for Approval') {
                needCheckList.add(opp);
            }
        }
        if (!needCheckList.isEmpty()) {
            checkAccount(needCheckList);
            checkOpp(needCheckList);
        }
    }

    private static void checkAccount(List<Opportunity> newList){
        String accountRequired = System.label.Opp_SubmitForApproval_AccountRequired;
        List<String> parts = accountRequired.split(';');
        String sqlStr = 'SELECT Id, Name ';
        if (!String.isBlank(accountRequired)) {
            for (String part : parts) {
                sqlStr = sqlStr+','+part;
            }
        }
        Set<Id> accountIdSet = new Set<Id>();
        for (Opportunity opp : newList) {
            if (String.isBlank(opp.AccountId)) {
                opp.addError('请在商机中添加关联客户');
            }
            accountIdSet.add(opp.AccountId);
        }
        sqlStr += ' FROM Account WHERE Id IN :accountIdSet';
        List<Account> accList = Database.query(sqlStr);
        Map<Id, Account> accMap = new Map<Id, Account>();
        for (Account acc : accList) {
            accMap.put(acc.Id, acc);
        }
        for (Opportunity opp : newList) {
            Account acc = accMap.get(opp.AccountId);
            for (String part : parts) {
                if (acc.get(part) == null || acc.get(part) == '') {
                    opp.addError('请在提交报备审批前，请至客户页面补充客户信息。');
                }
            }
        }
    }
    private static void checkOpp(List<Opportunity> newList){
        String oppRequired = System.label.Opp_SubmitForApproval_OppRequired;
        List<String> parts = oppRequired.split(';');
        for (Opportunity opp : newList) {
            for (String part : parts) {
                if (opp.get(part) == null || opp.get(part) == '') {
                    opp.addError('请在提交报备审批前，将商机信息补充完整。。');
                }
            }
        }
    }

    /**
     * 处理商机审批状态变更，回显审批状态到客户
     */
    private void handleApprovalStatusChangeForAccountLock(List<Opportunity> newOpportunities, Map<Id, Opportunity> oldOpportunities) {
        Map<Id, String> accountStatusToUpdate = new Map<Id, String>();

        // 筛选审批状态从Draft变为Submitted for Approval或Approved的商机
        for (Opportunity newOppo : newOpportunities) {
            Opportunity oldOppo = oldOpportunities.get(newOppo.Id);

            // 仅处理从Draft变为Submitted for Approval或Approved的状态变更
            if (oldOppo.ApprovalStatus__c == 'Draft' &&
                (newOppo.ApprovalStatus__c == 'Submitted for Approval' || newOppo.ApprovalStatus__c == 'Approved')) {

                // 收集需要更新的客户ID和对应的审批状态
                accountStatusToUpdate.put(newOppo.AccountId, newOppo.ApprovalStatus__c);
            }
        }

        // 若没有需要更新的客户，直接返回
        if (accountStatusToUpdate.isEmpty()) {
            return;
        }

        // 调用客户审批状态更新方法
        updateAccountApprovalLockStatus(accountStatusToUpdate);
    }

    /**
     * 更新客户的审批状态字段（OppoApprovalStatus__c）
     * @param accountStatusMap 客户ID和对应审批状态的映射
     */
    private void updateAccountApprovalLockStatus(Map<Id, String> accountStatusMap) {
        if (accountStatusMap.isEmpty()) return;
        System.debug('accountStatusMap***:'+accountStatusMap.size());

        // 准备批量更新的客户记录
        List<Account> accountsToUpdate = new List<Account>();
        for (Id accId : accountStatusMap.keySet()) {
            String approvalStatus = accountStatusMap.get(accId);

            // 更新客户的审批状态字段
            accountsToUpdate.add(new Account(
                Id = accId,
                OppoApprovalStatus__c = approvalStatus // 回显商机的审批状态到客户
            ));
        }

        // 批量更新客户记录
        if (!accountsToUpdate.isEmpty()) {
            update accountsToUpdate;
            System.debug('已更新客户审批状态: ' + accountsToUpdate.size() + ' 条记录');
        }
    }

}