public with sharing class CreateInstallmentBillPaymentSchedule implements Schedulable, Database.Batchable<SObject> {
    public void execute(SchedulableContext sc) {
        Database.executeBatch(this);
    }
    
    public Database.QueryLocator start(Database.BatchableContext bc) {
        return Database.getQueryLocator([
            SELECT Id,Contract__r.AccountId,Contract__r.SapContractNo__c,Contract__r.SettlementCurrency__c,
            Expense_Settlement_Method__c,PaymentCycle__c,Contract__r.Non_Standard_Rate__c,
            (SELECT id,Payment_Notice_Amt__c,PaymentDate__c,Period_EndDate__c,Period_StartDate__c FROM CollectionPlans__r WHERE alreadySendBilling__c = false) FROM CollectionPlan__c WHERE Expense_Settlement_Method__c ='Installment' and UnCreateBillingNum__c > 0 ]);
    }
    
    public void execute(Database.BatchableContext bc, List<CollectionPlan__c> scope) {
        List<BillPaymentAdvice__c> paymentAdvices = new List<BillPaymentAdvice__c>();
        for(CollectionPlan__c plan : scope){
            if(plan.CollectionPlans__r == null || plan.CollectionPlans__r.size() == 0) continue;
                for(CollectionPlanLine__c line:plan.CollectionPlans__r){
                    if(line.Period_StartDate__c.addDays(-2) == system.today()){
                        BillPaymentAdvice__c bpa = new BillPaymentAdvice__c();
                        bpa.Account__c = plan.Contract__r.AccountId;
                        bpa.Contract_Number__c = plan.Contract__r.ContractAutoNo__c;  
                        bpa.SAP_Contract_Number__c = plan.Contract__r.SapContractNo__c;
                        bpa.Contract_Currency__c =  plan.Contract__r.ContractCurrency__c;
                        bpa.Payment_Currency__c = plan.Contract__r.SettlementCurrency__c;
                        bpa.ExpenseSettlement_Method__c = plan.Expense_Settlement_Method__c;
                        //bpa.PaymentCycle__c = plan.PaymentCycle__c;
                        bpa.PayableAmount__c =line.Payment_Notice_Amt__c;
                        bpa.Exchange_Rate__c = plan.Contract__r.Non_Standard_Rate__c ==null?0: Decimal.valueOf(plan.Contract__r.Non_Standard_Rate__c);
                        //bpa.Due_Date__c = line.PaymentDate__c;
                        bpa.Collection_Plan_Line__c = line.Id;
                        //bpa.EndDate__c = line.Period_EndDate__c;
                        //bpa.StartDate__c = line.Period_StartDate__c;
                        bpa.Creat_Date__c = line.Period_EndDate__c;
                        bpa.UniqueKey__c = bpa.Contract_Number__c  +'-'+  DataProcessTool.formatSAPDate(bpa.Creat_Date__c);
                        bpa.RecordTypeId = Schema.SObjectType.BillPaymentAdvice__c.getRecordTypeInfosByDeveloperName().get('ClosedContractPaymentNotice').getRecordTypeId();
                        paymentAdvices.add(bpa);
                    }
                }
        }
        
        if(paymentAdvices.size() > 0){
            insert paymentAdvices;
        }
    }
    
    public void finish(Database.BatchableContext bc) {
    }
}