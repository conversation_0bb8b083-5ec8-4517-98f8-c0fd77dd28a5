public with sharing class SyncContractChangeToSAPQueue implements Queueable,Database.AllowsCallouts {
    private Set<id> ChangeContractIds;
    public SyncContractChangeToSAPQueue(Set<id> ChangeContractIds) {
        this.ChangeContractIds = ChangeContractIds;
    }

    public void execute(QueueableContext context) {
        
        if(ChangeContractIds.size() > 0 && ChangeContractIds.size() <= 10){
            try{
                for(Id changeContractId:ChangeContractIds){
                    Interface_CRMSyncContractChangeToSAP syncSAP = new Interface_CRMSyncContractChangeToSAP(changeContractId);
                }
            }catch(Exception e){
                CWUtility.createQueueLog('SyncContractChangeToSAPQueue','Failed',ChangeContractIds,e.getMessage());
            }
            
        }else{
            CWUtility.createQueueLog('SyncContractChangeToSAPQueue','Failed',ChangeContractIds,'ChangeContractIds is null or size is 0');
        }
        
    }
}