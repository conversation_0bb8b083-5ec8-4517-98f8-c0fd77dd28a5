global with sharing class Interface_SAPSyncPaymentToSFDC extends Interface_InboundBase{
    public Map<String, Account> accountNo_AccountMap;
    public Map<String, CollectionPlanLine__c> contract_planLineListMap;
    public Map<String, Product2> productCode_ProductMap;
    public Map<String, CollectionPlanLineProduct__c> lineProductMap;
    global override Map<String, Object> execute(Interface_InboundParam param){
        Map<String, Object> resultMap = new Map<String, Object>();
        
        //upsert  records
        try {
            Interface_PaymentObject record = (Interface_PaymentObject)JSON.deserialize(param.dataString, Interface_PaymentObject.class);
            List<BillPaymentAdvice__c> paymentList = new List<BillPaymentAdvice__c>();
            List<BillPaymentAdviceDetail__c> paymentDetailList = new List<BillPaymentAdviceDetail__c>();
            resultMap.put('uuid',record.uuid);

            //获取所有关联的客户ID
            Set<String> accountNo = new Set<String>();
            //Salesforce 合同号
            Set<String> contractNo = new Set<String>();
            //产品编码
            Set<String> productCode = new Set<String>();

            
            for (Interface_PaymentObject.Header header : record.header) {
                if (String.isBlank(header.kunnr)) {
                    throw new DmlException ('客户编号必填'); // 抛出异常
                }
                if (String.isBlank(header.bstkd)) {
                    throw new DmlException ('合同编号必填'); // 抛出异常
                }
                accountNo.add(header.kunnr);
                contractNo.add(header.bstkd);
                if (!header.item.isEmpty()) {
                    for (Interface_PaymentObject.Item item : header.item) {
                        if (String.isNotBlank(item.matnr)) {
                            productCode.add(item.matnr);
                        }else{
                            throw new DmlException ('物料编码必填');
                        }
                    }
                }
            }
            //根据合同编码获取系统中合同下的全部收款计划明细
            if (!contractNo.isEmpty()) {
                contract_planLineListMap = getCollectionPlanLineByContractNo(contractNo);
                //查询合同下的所有收款计划明细产品
                lineProductMap = getCollectionPlanLineProduct(contractNo);
            }
            

            //根据客户编号获取客户信息
            if (!accountNo.isEmpty()) {
                accountNo_AccountMap = getAccountByAccountNo(accountNo);
            }
            //根据产品编号查找产品
            if (!productCode.isEmpty()) {
                productCode_ProductMap = Service_Product.searchProductByProductCode(productCode);
            }
            
            //处理数据
            for (Interface_PaymentObject.Header header : record.header) {
                paymentList.add(generatePayment(header));
                List<Interface_PaymentObject.Item> items = header.item;
                if (!items.isEmpty()) {
                    paymentDetailList.addAll(generatePaymentDetail(header));
                }
                
            }
            Database.upsert(paymentList,BillPaymentAdvice__c.UniqueKey__c);
            Database.upsert(paymentDetailList,BillPaymentAdviceDetail__c.UniqueKey__c);

            resultMap.put('msgty','S');
            resultMap.put('msgtx', 'Success');
             
           
        } catch (Exception e) {
            
            resultMap.put('msgty','E');
            resultMap.put('msgtx', e.getMessage());
          
        }
        resultMap.put('msgid', '');
        resultMap.put('msgno', '');
        resultMap.put('sapnum','');
        resultMap.put('field1','');
        resultMap.put('field2','');
        resultMap.put('field3',''); 

        // 设置响应头和状态码
        RestContext.response.addHeader('Content-Type', 'application/json');
        RestContext.response.statusCode = 200;
        RestContext.response.responseBody = Blob.valueOf(JSON.serialize(resultMap));
        return resultMap;
    }

    public BillPaymentAdvice__c generatePayment(Interface_PaymentObject.Header header){

        BillPaymentAdvice__c payment = new BillPaymentAdvice__c();
        payment.RecordTypeId = Schema.getGlobalDescribe()
                     .get('BillPaymentAdvice__c')
                     .getDescribe()
                     .getRecordTypeInfosByDeveloperName()
                     .get('NormalPaymentAdvice')
                     .getRecordTypeId();
        
        payment.Billing_Number__c = header.vbeln;
        payment.Billing_Type__c = header.fkart;
        payment.Account_Number__c = header.kunnr;//存储接口传的客户编码
        payment.Account__c = accountNo_AccountMap.get(header.kunnr).Id;
        if (!String.isBlank(header.fkdat)) {
            payment.Billing_Date__c = DataProcessTool.parseSAPDate(header.fkdat);
        }
        if (String.isBlank(header.erdat)) {
            throw new DmlException('缺少必填字段：erdat');
        }
        payment.Creat_Date__c = DataProcessTool.parseSAPDate(header.erdat);
        payment.Company_Code__c = header.bukrs;
        payment.PaymentTerm__c = header.zterm;
        payment.Payment_Currency__c = header.waerk;
        if (!String.isBlank(header.netwr)) {
            payment.Total_Amount__c = Decimal.valueOf(header.netwr.trim());
        }
        
        if (!String.isBlank(header.mwsbk)) {
            payment.Total_Tax_Amount__c = Decimal.valueOf(header.mwsbk.trim());
        }
        payment.PayableAmount__c = payment.Total_Amount__c+payment.Total_Tax_Amount__c;
        payment.SAP_Contract_Number__c = header.zvbelv;
        payment.Contract_Number__c = header.bstkd;
        if (!String.isBlank(header.zexrate)) {
           payment.Exchange_Rate__c = Decimal.valueOf(header.zexrate.trim());
        }
        payment.Contract_Currency__c = header.zconcur;
        payment.Adjustment_Note__c = header.augru_auft;
        payment.UniqueKey__c = header.bstkd+'-'+header.erdat;
        if (!contract_planLineListMap.containsKey(payment.UniqueKey__c)) {
            throw new DmlException('未查找到关联的收款计划明细');
        }else{
            payment.Collection_Plan_Line__c = contract_planLineListMap.get(payment.UniqueKey__c).Id;
            payment.Contract__c = contract_planLineListMap.get(payment.UniqueKey__c).CollectionPlan__r.Contract__c;
        }
        
        return payment;

    }
    public List<BillPaymentAdviceDetail__c> generatePaymentDetail(Interface_PaymentObject.Header header){
        List<BillPaymentAdviceDetail__c> recordList = new List<BillPaymentAdviceDetail__c>();
        for (Interface_PaymentObject.Item item : header.item) {
            BillPaymentAdviceDetail__c record = new BillPaymentAdviceDetail__c();
            record.Item_Key__c = item.zitem;
            record.Items_No__c = item.posnr;
            record.Product_Code__c = item.matnr;
            if (!String.isBlank(item.matnr) && !productCode_ProductMap.containsKey(record.Product_Code__c)) {
                throw new DmlException('系统中未查找到关联的产品记录');
            }
            if (!String.isBlank(item.matnr) && productCode_ProductMap.containsKey(record.Product_Code__c)) {
                record.Product__c = productCode_ProductMap.get(record.Product_Code__c).Id;
            }
            if (!String.isBlank(item.fkimg)) {
                record.Quantity__c = Decimal.valueOf(item.fkimg.trim());
            }
            
            record.Unit__c = item.meins;
            if (!String.isBlank(item.znetwr)) {
                record.UnitPrice__c = Decimal.valueOf(item.znetwr.trim());//单价（不含税）
                
                if (!String.isBlank(item.fkimg)) {
                    record.Total_Amount__c = record.UnitPrice__c * record.Quantity__c;//总金额（不含税）
                }
            }
            if (!String.isBlank(item.kbetr)) {
                record.Tax_Rate__c = Decimal.valueOf(item.kbetr.trim());//税率
                if (!String.isBlank(item.znetwr) && !String.isBlank(item.fkimg)) {
                    record.UnitPrice_tax__c = record.UnitPrice__c *(1+Decimal.valueOf(item.kbetr.trim())/100);
                    record.Tax__c = record.Total_Amount__c * record.Tax_Rate__c/100;//总价税额
                    record.Total_Amount_Tax__c =  record.Total_Amount__c + record.Tax__c;//总金额（含税）
                }
                
            }

            record.UniqueKey__c = header.bstkd+'-'+header.erdat+'-'+item.matnr;
            record.BillPaymentAdvice__r = new BillPaymentAdvice__c(UniqueKey__c=header.bstkd+'-'+header.erdat);
            if (lineProductMap.containsKey(record.UniqueKey__c)) {
                 record.CollectionPlanLineProduct__c = lineProductMap.get(record.UniqueKey__c).Id;
            }
            recordList.add(record);
        }
        return recordList;
    }

    //根据SFDC合同编码，查找收款计划明细产品
    public Map<String, CollectionPlanLineProduct__c> getCollectionPlanLineProduct(Set<String> contractNoSet){
        Map<String, CollectionPlanLineProduct__c> tempMap = new Map<String, CollectionPlanLineProduct__c>();
        List<CollectionPlanLineProduct__c> lineProductList = [SELECT Id,CollectionPlanLine__r.CollectionPlan__r.Contract__r.ContractAutoNo__c,
                                                                    CollectionPlanLine__r.Period_EndDate__c,
                                                                    ContractProduct__r.Product__r.ProductCode 
                                                            FROM CollectionPlanLineProduct__c 
                                                            WHERE CollectionPlanLine__r.CollectionPlan__r.Contract__r.ContractAutoNo__c IN :contractNoSet];
        if (!lineProductList.isEmpty()) {
            for (CollectionPlanLineProduct__c lineProduct : lineProductList) {
                String uniqueKey = lineProduct.CollectionPlanLine__r.CollectionPlan__r.Contract__r.ContractAutoNo__c
                                    +'-'+DataProcessTool.formatSAPDate(lineProduct.CollectionPlanLine__r.Period_EndDate__c)
                                    +'-'+lineProduct.ContractProduct__r.Product__r.ProductCode;
                tempMap.put(uniqueKey,lineProduct);
            }
        }
        return tempMap;
        
    }

    //根据SFDC合同编码，查找 收款计划明细
    public Map<String, CollectionPlanLine__c> getCollectionPlanLineByContractNo(Set<String> contractNoSet){
        Map<String, CollectionPlanLine__c> lineMap = new Map<String, CollectionPlanLine__c>();
        for (CollectionPlanLine__c line : [SELECT Id,CollectionPlan__r.Contract__c,CollectionPlan__r.Contract__r.ContractAutoNo__c,
                                     Period_StartDate__c,Period_EndDate__c,PaymentDate__c 
                              FROM CollectionPlanLine__c 
                              WHERE CollectionPlan__r.Contract__r.ContractAutoNo__c IN : contractNoSet]) {
            lineMap.put(line.CollectionPlan__r.Contract__r.ContractAutoNo__c+'-'+DataProcessTool.formatSAPDate(line.Period_EndDate__c),line);
            
        }
        return lineMap;
    }

    //根据SAP客户编码，查找系统中得客户
    public Map<String, Account> getAccountByAccountNo(Set<String> accountNoSet){
        Map<String, Account> accountMap = new Map<String, Account>();
        List<Account> accList = [SELECT Id,SAP_Num__c FROM Account WHERE SAP_Num__c IN :accountNoSet];
        if (accList.isEmpty()) {
            throw new DmlException ('系统中未查找到客户'); // 抛出异常
        }
        for (Account acc : accList) {
            accountMap.put(acc.SAP_Num__c, acc);
        }
        return accountMap;
    }
    
}