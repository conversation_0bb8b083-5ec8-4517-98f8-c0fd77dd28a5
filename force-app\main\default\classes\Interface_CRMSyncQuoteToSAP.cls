public with sharing class Interface_CRMSyncQuoteToSAP {
    public static Interface_OutboundParam param {get;set;}

    @future(callout=true)
    public static void doSyncQuoteToSAP(String quoteId) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncQuoteToSAP';
        param.endpoint ='callout:SAP_API_Credential'; //改动
        param.retryTimes = 3;
        param.recordIdSet = new Set<id>{quoteId};
        param.targetObject = 'Quote';
        // param.httpMethod = 'POST';
        // String token = '';
      
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(quoteId);      

        Interface_OutboundExecutor syncContract = new Interface_OutboundExecutor(param);
        String body = syncContract.execute();
        System.debug('body'+body);
        
    }

    public static String getRequestHeader(){
        // String encodedCredentials = CWUtility.getSAPEncodedCredentials();
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        // headerMap.put('sap-client', '110'); //改动
        //headerMap.put('Authorization', 'Bearer '+token);
        //headerMap.put('Authorization', 'Basic ' + encodedCredentials);
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    public static String getRequestBody(String quoteId){
        QuoteWrpper wrapper = new QuoteWrpper();
        wrapper.req = new Req();
        Quote qObj = [SELECT Id,QuoteNumber,Account.SAP_Num__c,
                        PartyB_Signing_Company__r.Customer_SimpleName__c , PartyB_Signing_Company__c 
                      FROM Quote 
                      WHERE Id =:quoteId];
        wrapper.req.ZQUNUM = qObj.QuoteNumber;
        wrapper.req.VKORG = qObj.PartyB_Signing_Company__r.Customer_SimpleName__c;
        wrapper.req.VTWEG = 'D1';
        wrapper.req.KUNNR = qObj.Account.SAP_Num__c;
        wrapper.req.Item = new List<QuoteLineObj>();
        Interface_CRMSyncQuoteToSAP.getGroupWrapper(quoteId,wrapper);
        Interface_CRMSyncQuoteToSAP.getsingleWrapper(quoteId,wrapper);
        System.debug('wrapper****:' + JSON.serialize(wrapper, true));
        return JSON.serialize(wrapper, true);
    }

    public static QuoteWrpper getGroupWrapper(String quoteId,QuoteWrpper wrapper) {
        // 获取报价单下的产品组
        List<QuoteLineItem> quoteLineItems = Database.query(
            'SELECT Id,IsExchangeRateDifference__c,ListPrice__c, Product2Id, Product2.Name, Product2.ProductCode, toLabel(Product2.Family), ' +
            'Product2.Description,CurrencyIsoCode, Product2.QuantityUnitOfMeasure, UnitPrice, ' +
            'Tax_Rate__c, Account_ID__c, Profit_Statement__c, Description, ' +
            'QuoteLineStartDate__c, QuoteLineEndDate__c, Region__c,IS_OneTimeFee__c, ' +
            'Product_Group__c, Product_Group__r.QuotationMethod_Ladder__c, ' +
            'Product_Group__r.QuotationMethod_Ladder__r.Method__c, ISGROUP__c, ' +
            'Product2.Level__c, Product2.ParentProduct__r.Name, Product2.ParentProduct__r.ParentProduct__r.Name, ' +
            'Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name ' +
            'FROM QuoteLineItem ' +
            'WHERE QuoteId = :quoteId AND Product_Group__c != null AND ISGROUP__c = true ' +
            'ORDER BY CreatedDate ASC'
        );
        
        // 收集所有产品ID，用于查询产品牌价
        Set<Id> quoteLineIds = new Set<Id>();
        List<Id> productIds = new List<Id>();
        Set<Id> ladderDiscountIds = new Set<Id>();
        Map<String,List<QuoteLineItem>> groupMap = new Map<String,List<QuoteLineItem>>();
        for (QuoteLineItem qli : quoteLineItems) {
            productIds.add(qli.Product2Id);
            quoteLineIds.add(qli.Id);
            ladderDiscountIds.add(qli.Product_Group__c);
            if(groupMap.containsKey(qli.Product_Group__c)){
                groupMap.get(qli.Product_Group__c).add(qli);
            }else{
                List<QuoteLineItem> qliList = new List<QuoteLineItem>();
                qliList.add(qli);
                groupMap.put(qli.Product_Group__c, qliList);
            }
        }
        // 查询产品牌价对象
        Map<String, ProductPrice__c> priceInfo = new Map<String, ProductPrice__c>();
        for (ProductPrice__c price : [
            SELECT Id, Product__c, Amount__c,Unit__c, CurrencyIsoCode
            FROM ProductPrice__c
            WHERE Product__c IN :productIds
        ]) {
            priceInfo.put(price.Product__c, price);   
        }
        
        // 查询所有报价方式数据
        Map<Id, Quotation_Method__c> groupQuotationMethods = new Map<Id, Quotation_Method__c>();
        
        // 保底金额+共享阶梯金额折扣落区组合报价方式
        Map<Id, Quotation_Method__c> groupMinimumAmountQuotations = new Map<Id, Quotation_Method__c>();
        List<Quotation_Method__c> minimumAmountQuotations = [
                SELECT Id,Product_Group__c,GuaranteedMin_Amount__c, Method__c,Minimum_Amout__c,Minimum_Price__c, Discount_Factor__c,LadderType__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN:ladderDiscountIds
                AND ISGROUP__c = true
        ];
        for(Quotation_Method__c qm : minimumAmountQuotations) {
            groupMinimumAmountQuotations.put(qm.Product_Group__c, qm);
        
        }
        // 获取所有相关的阶梯报价行
        Map<Id, List<Ladder_Line__c>> ladderLinesByLadderDiscount = new Map<Id, List<Ladder_Line__c>>();
        if (!ladderDiscountIds.isEmpty()) {
            for (Ladder_Line__c line : [
                SELECT Id,Name, Product_Group__c, Up_Limit__c, Down_Limit__c, 
                        Unit__c, Discount__c, Calculation_Method__c
                FROM Ladder_Line__c
                WHERE Product_Group__c IN :ladderDiscountIds
                
            ]) {
                if (!ladderLinesByLadderDiscount.containsKey(line.Product_Group__c)) {
                    ladderLinesByLadderDiscount.put(line.Product_Group__c, new List<Ladder_Line__c>());
                }
                ladderLinesByLadderDiscount.get(line.Product_Group__c).add(line);
            }
        }
        for(String pg : groupMap.keySet()){
            List<QuoteLineItem> qliList = groupMap.get(pg);
            Quotation_Method__c qm = groupMinimumAmountQuotations.get(pg);
            for(QuoteLineItem qli : qliList){
                QuoteLineObj lineObj = new QuoteLineObj();
                lineObj.ZQUOTYPE = qm.Method__c;
                lineObj.ZAREA = qli.Region__c;
                lineObj.MATNR = qli.Product2.ProductCode;
                lineObj.KBETR = qm.Minimum_Price__c;
                lineObj.KONWA = qli.CurrencyIsoCode;
                lineObj.KPEIN = qli.UnitPrice;
                lineObj.KMEIN = qli.Product2.QuantityUnitOfMeasure;
                lineObj.ZEXCRD = qli.IsExchangeRateDifference__c;
                lineObj.ZBPRICE = qli.ListPrice__c==null?0:qli.ListPrice__c;
                lineObj.ZQUANT = qm.Minimum_Amout__c;
                lineObj.ZGXZHBS ='X';
                lineObj.ZSHGRO = pg;
                lineObj.ZTRDTY = qm.LadderType__c;
                
                // lineObj.ZBADPR =
                // lineObj.ZBADTY = 
                lineObj.ZDISCOUNT = qm.Discount_Factor__c;
                lineObj.ZOTAMT =qli.IS_OneTimeFee__c=='是'?'X':'';
                lineObj.DATAB = dateFormat(qli.QuoteLineStartDate__c);
                lineObj.DATBI = dateFormat(qli.QuoteLineEndDate__c);
                lineObj.ZZGDFD = qm.Fixed_Rebate__c;
                lineObj.ZVOUCH = qm.Credit__c;
                lineObj.ZZCASH = qm.Cash_Reduce__c;
                lineObj.Item1 = new List<LadderObj>();
                if(ladderLinesByLadderDiscount.containsKey(pg) && ladderLinesByLadderDiscount.get(pg).size()>0){
                    for(Ladder_Line__c line: ladderLinesByLadderDiscount.get(pg)){
                        LadderObj lobj = new LadderObj();
                        lobj.KLFN1 = line.Name;
                        lobj.KSTBM = line.Up_Limit__c;
                        lobj.KBETR = line.Discount__c; 
                        lineObj.Item1.add(lobj);
                    }
                }
                

                
                wrapper.req.Item.add(lineObj);
            }
        }
        return wrapper;
    }

    public static QuoteWrpper getsingleWrapper(String quoteId,QuoteWrpper wrapper) {
        List<QuoteLineItem> quoteLineItems = Database.query(
                'SELECT Id,IsExchangeRateDifference__c, Product2Id, Product2.Name, Product2.ProductCode, toLabel(Product2.Family), ' +
                'Product2.Description,ListPrice__c, Product2.QuantityUnitOfMeasure, UnitPrice, ' +
                'Tax_Rate__c,CurrencyIsoCode, Account_ID__c, Profit_Statement__c, Description, ' +
                'QuoteLineStartDate__c, QuoteLineEndDate__c, Region__c, ' +
                'Product_Group__c, IS_OneTimeFee__c,' +
                'Product2.Level__c, Product2.ParentProduct__r.Name, Product2.ParentProduct__r.ParentProduct__r.Name, ' +
                'Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name ' +
                'FROM QuoteLineItem ' +
                'WHERE QuoteId = :quoteId AND ISGROUP__c = false AND Product_Group__c = null AND Product2Id != null ' +
                'ORDER BY CreatedDate ASC'
            );
            // 收集所有产品ID，用于查询产品牌价
            Set<Id> productIds = new Set<Id>();
            Set<Id> qliList = new Set<Id>();
            for (QuoteLineItem qli : quoteLineItems) {
                if (qli.Product2Id != null) {
                    productIds.add(qli.Product2Id);
                    qliList.add(qli.Id);
                }
            }
            // 查询产品牌价对象
            Map<String, ProductPrice__c> priceInfo = new Map<String, ProductPrice__c>();

            for (ProductPrice__c price : [
                SELECT Id, Product__c,Unit__c, Amount__c, CurrencyIsoCode
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                priceInfo.put(price.Product__c, price);
            }
             Map<String,Quotation_Method__c> qmMap = new Map<String,Quotation_Method__c>();  
            for (Quotation_Method__c qm : [
                SELECT Id,GuaranteedMin_Amount__c,Quote_Line_Item_ID__c, Method__c,Minimum_Price__c,Minimum_Amout__c, Discount_Factor__c,LadderType__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c
                FROM Quotation_Method__c
                WHERE Quote_Line_Item_ID__c IN :qliList AND ISGROUP__c = false
            ]) {
                qmMap.put(qm.Quote_Line_Item_ID__c, qm);
            }
            // 获取阶梯行数据
            Map<Id, List<Ladder_Line__c>> ladderLinesByLadder = new Map<Id, List<Ladder_Line__c>>();
            List<Ladder_Line__c> ladderLines = [
                SELECT Id,Name, Down_Limit__c,QuoteLineItem__c, Up_Limit__c, Unit__c, Discount__c, Calculation_Method__c
                FROM Ladder_Line__c
                WHERE QuoteLineItem__c IN: qliList
                ORDER BY Down_Limit__c ASC
            ];
            for(Ladder_Line__c ln : ladderLines) {
                if (!ladderLinesByLadder.containsKey(ln.QuoteLineItem__c)) {
                    ladderLinesByLadder.put(ln.QuoteLineItem__c, new List<Ladder_Line__c>());
                }
                ladderLinesByLadder.get(ln.QuoteLineItem__c).add(ln);
            }
            for(QuoteLineItem qli : quoteLineItems){
                Quotation_Method__c qm = qmMap.get(qli.Id);
                QuoteLineObj lineObj = new QuoteLineObj();
                lineObj.ZQUOTYPE = qm.Method__c;
                lineObj.ZAREA = qli.Region__c;
                lineObj.MATNR = qli.Product2.ProductCode;
                lineObj.KBETR = qm.Minimum_Price__c;
                lineObj.KONWA = qli.CurrencyIsoCode;
                lineObj.KPEIN = qli.UnitPrice;
                lineObj.KMEIN = qli.Product2.QuantityUnitOfMeasure;
                lineObj.ZEXCRD = qli.IsExchangeRateDifference__c;
                lineObj.ZBPRICE = qli.ListPrice__c==null?0:qli.ListPrice__c;
                lineObj.ZQUANT = qm.Minimum_Amout__c;
                // lineObj.ZGXZHBS ='X';
                // lineObj.ZSHGRO = pg;
                lineObj.ZTRDTY = qm.LadderType__c;
                
                // lineObj.ZBADPR =
                // lineObj.ZBADTY = 
                lineObj.ZDISCOUNT = qm.Discount_Factor__c;
                lineObj.ZOTAMT =qli.IS_OneTimeFee__c=='是'?'X':'';
                lineObj.DATAB = dateFormat(qli.QuoteLineStartDate__c);
                lineObj.DATBI = dateFormat(qli.QuoteLineEndDate__c);
                lineObj.ZZGDFD = qm.Fixed_Rebate__c;
                lineObj.ZVOUCH = qm.Credit__c;
                lineObj.ZZCASH = qm.Cash_Reduce__c;
                lineObj.Item1 = new List<LadderObj>();
                if(ladderLinesByLadder.containsKey(qli.Id) && ladderLinesByLadder.get(qli.Id).size() >0){
                    for(Ladder_Line__c line: ladderLinesByLadder.get(qli.Id)){
                        System.debug('line'+line);
                        LadderObj lobj = new LadderObj();
                        lobj.KLFN1 = line.Name;
                        lobj.KSTBM = line.Up_Limit__c;
                        lobj.KBETR = line.Discount__c; 
                        lineObj.Item1.add(lobj);
                    }
                }
                
                wrapper.req.Item.add(lineObj);
            }
            return wrapper;
    }
       

    public static String dateFormat(Date d){
        String str = '';
        if(d != null){
            str = DateTime.newInstance(d.year(), d.month(), d.day()).format('yyyyMMdd');
        }
        return str;
    }

    public class QuoteWrpper{
        public String uuid;
        public String znumb;//接口编号
        public String fsysid;//请求系统
        public Req req {get; set;}//请求体
        public QuoteWrpper() {
            uuid = CWUtility.generateUUID();
            znumb = 'SD013';
            fsysid = 'SALESFORCE';
        }   
    }
    public class Req{
        public String ZQUNUM {get; set;} 
        public String VKORG {get; set;}  
        public String VTWEG {get; set;}  
        public String KUNNR {get; set;}  
   
        public List<QuoteLineObj> Item {get; set;}  
        
    }

    public class QuoteLineObj{
        public String ZQUOTYPE {get; set;}
        public String ZAREA {get; set;}
        public String MATNR {get; set;}
        public Decimal KBETR {get; set;}
        public String KONWA {get; set;}
        public Decimal KPEIN {get; set;}
        public String KMEIN {get; set;}
        public String ZEXCRD {get; set;}
        public String ZGXZHBS {get; set;}
        public Decimal ZBPRICE {get; set;}
        public Decimal ZQUANT {get; set;}
        public String ZTRDTY {get; set;}
        public String ZBADPR {get; set;}
        public String ZBADTY {get; set;}
        public Decimal ZDISCOUNT {get; set;}
        public String ZOTAMT {get; set;}
        public String DATAB {get; set;}
        public String ZSHGRO {get; set;}
        public String DATBI {get; set;}
        public Decimal ZZGDFD {get; set;}
        public Decimal ZVOUCH {get; set;}
        public Decimal ZZCASH {get; set;}
        public List<LadderObj> Item1 {get; set;}
    }

    public class LadderObj{
        public String KLFN1 {get; set;} 
        public Decimal KSTBM {get; set;} 
        public Decimal KBETR {get; set;} 
    }
}