public with sharing class CWUtility {

    public static String SAP_Auth_Credential = null;
    static {
        if (isSandbox()) {
            SAP_Auth_Credential = 'callout:SAP_API_Credential';
        } else {
            SAP_Auth_Credential = '';
        }
    }

    public static Boolean isSandbox() {
        return [SELECT IsSandbox FROM Organization LIMIT 1].IsSandbox;
    }

    /********************************************************************
    * Purpose: 将收款计划数据转化为Json
    * Param: 合同ID
    * Return: Json String
    ********************************************************************/
    public static String getPlanJosn(String contractId){
        List<CollectionPlanLine__c> planList = [
            SELECT id,InActive__c,CollectionPlan__r.Contract__r.SapContractNo__c,CollectionPlan__r.Contract__r.ContractAutoNo__c,Description__c,Period_StartDate__c,Period_EndDate__c,
            (Select id,Product__r.Name,Product__r.ProductCode,StartDate__c,EndDate__c,DaysCount__c From CollectionPlanLines__r) FROM CollectionPlanLine__c WHERE CollectionPlan__r.Contract__c =:contractId];
        if(!planList.isEmpty()){
            List<CollectionPlanLine> planObjList = new List<CollectionPlanLine>();
            for(CollectionPlanLine__c plan:planList){
                CollectionPlanLine obj = new CollectionPlanLine();
                obj.id = plan.id;
                obj.planName = plan.Description__c;
                obj.contractNo = plan.CollectionPlan__r.Contract__r.ContractAutoNo__c;
                obj.contractSapNo = plan.CollectionPlan__r.Contract__r.SapContractNo__c;
                obj.periodStartDate = DataProcessTool.formatSAPDate(plan.Period_StartDate__c);
                obj.periodEndDate = DataProcessTool.formatSAPDate(plan.Period_EndDate__c);
                obj.inActive = plan.InActive__c ? 'X':'';
                for(CollectionPlanLineProduct__c childPlan:plan.CollectionPlanLines__r){
                    CollectionPlanLineProduct childObj = new CollectionPlanLineProduct();
                    childObj.id = childPlan.id;
                    childObj.productName = childPlan.Product__r.Name;
                    childObj.productSapNumber = childPlan.Product__r.ProductCode;
                    childObj.startDate = DataProcessTool.formatSAPDate(childPlan.StartDate__c);
                    childObj.endDate = DataProcessTool.formatSAPDate(childPlan.EndDate__c);
                    childObj.daysCount = String.valueOf(childPlan.DaysCount__c);
                    obj.poductItems.add(childObj);
                }
                planObjList.add(obj);
            }
            return JSON.serialize(planObjList);
        }
        return '';
    }

    /********************************************************************
    * Purpose: 实例化对象
    * Param: sobjectName 需要实例的对象API
    * Return: 实例的对象
    ********************************************************************/
    public static Sobject instanceSobjectByName(String sobjectName) {
        Schema.SObjectType convertType = Schema.getGlobalDescribe().get(sobjectName);
        return convertType.newSObject(); 
    }

    /********************************************************************
    * Purpose: 新建接口日志
    * Param: logList 接口日志数据
    ********************************************************************/
    public static void insertLog(List<Interface_Log__c> logList){
        // 判断是否有日志数据
        if (logList == null || logList.size() <= 0){
            return;
        }

        // 汇总接口日志的文件
        List<List<Attachment>> attactmentsList = new List<List<Attachment>>();
        // 汇总需要插入的日志数据
        List<Interface_Log__c> insertLogList = new List<Interface_Log__c>();
        for (Interface_Log__c logInfo : logList){
            logInfo.CallTime__c = system.now();
            // 汇总需要新建的文件
            List<Attachment> attachmentList = new List<Attachment>();

            // 请求的头信息
            if (logInfo.RequestHeader__c != null && logInfo.RequestHeader__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.RequestHeader__c);
                attachmentList.add(sc);
                logInfo.RequestHeader__c = logInfo.RequestHeader__c.substring(0, 100000);
            }

            // 请求的内容
            if (logInfo.RequestBody__c != null && logInfo.RequestBody__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.RequestBody__c);
                attachmentList.add(sc);
                logInfo.RequestBody__c = logInfo.RequestBody__c.substring(0, 100000);
            }

            // 相应的内容
            if (logInfo.ResponseBody__c != null && logInfo.ResponseBody__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.ResponseBody__c);
                attachmentList.add(sc);
                logInfo.ResponseBody__c = logInfo.ResponseBody__c.substring(0, 100000);
            }

            // 错误信息
            if (logInfo.ErrorMessage__c != null && logInfo.ErrorMessage__c.length() >= 100000){
                Attachment sc = CWUtility.generateAttachment(logInfo.ErrorMessage__c);
                attachmentList.add(sc);
                logInfo.ErrorMessage__c = logInfo.ErrorMessage__c.substring(0, 100000);
            }

            // 汇总接口日志
            insertLogList.add(logInfo);

            // 汇总接口日志的相关文件
            attactmentsList.add(attachmentList);
        }
        if (insertLogList.size() > 0){
            try {
                // 插入接口日志
                insert insertLogList;

                // 汇总插入的接口日志文件
                List<Attachment> insertAttachmentList = new List<Attachment>();

                // 循环已插入的接口日志
                for (Integer i = 0; i < insertLogList.size(); i++) {
                    Interface_Log__c logInfo = insertLogList[i];
                    System.debug('logInfo.Id***'+logInfo.Id);
                    // 取对应日志的文件数据
                    if (attactmentsList != null && attactmentsList.size() > 0
                        && attactmentsList[i] != null
                        && attactmentsList[i].size() > 0) {
                        List<Attachment> attachmentList = attactmentsList[i];
                        
                        // 赋值文件的主对象Id
                        for (Attachment attachment : attachmentList){
                            attachment.ParentId = logInfo.Id;
                        }
                        insertAttachmentList.addAll(attachmentList);
                    }
                }
                // 插入接口日志文件
                if (insertAttachmentList.size() > 0){
                    insert insertAttachmentList;
                }
            }catch(Exception e) {
                System.debug(LoggingLevel.INFO, '*** e.getMessage(): ' + e.getMessage());
                System.debug(LoggingLevel.INFO, '*** e.getStackTraceString(): ' + e.getStackTraceString());
            }
        }
    }

    /********************************************************************
    * Purpose: 生成文件
    * Param: content 文件里的内容
    * Return: 需要生成的文件
    ********************************************************************/
    public static Attachment generateAttachment(String content) {
        Attachment sc = new Attachment();
        sc.Name = 'fileContent';
        sc.Body = Blob.valueOf(content);
        return sc;
    }

    /********************************************************************
    * Purpose: 通过时间戳生成接口uuid
    * Return: 返回uuid
    ********************************************************************/
    public static String generateUUID() {
        Blob b = Crypto.generateAesKey(128);
        String uuid = EncodingUtil.convertToHex(b);
        return uuid;
    }
     /********************************************************************
    * Purpose: 发送邮件
    * Param: 邮件内容
    * Return: 
    ********************************************************************/
    public static void sendEmailToAdmin(String body){
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        List<String> adminEmails = new List<String>{'<EMAIL>','<EMAIL>','<EMAIL>'}; 
        mail.setToAddresses(adminEmails);
        mail.setSubject('【接口发送监控提醒】同步失败');
        mail.setPlainTextBody(body);
        try {
            Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{mail});
        } catch(Exception e) {
            System.debug('发送邮件失败: ' + e.getMessage());
        }
    }

    /********************************************************************
    * Purpose: 创建queue执行的日志
    * Param: 
    * queueName 名称
    * Status 状态
    * recordIds 数据Ids
    * errorMsg 报错信息
    ********************************************************************/
    public static void createQueueLog(String queueName,String Status,Set<Id> recordIds,String errorMsg){
        Interface_Log__c queueLog = new Interface_Log__c();
        queueLog.InterfaceName__c = queueName;
        queueLog.Status__c = Status ;
        queueLog.InterfaceType__c = 'queue';
        queueLog.RecordIDs__c = String.join(recordIds, ';');
        queueLog.CallTime__c = System.now();
        queueLog.ErrorMessage__c = errorMsg;
        insert queueLog;
    }

    /********************************************************************
    * Purpose: 付款计划的内部类
    *********************************************************************/
    public class CollectionPlanLine{
        public String id;
        public String planName;
        public String contractNo;
        public String contractSapNo;
        public String periodStartDate;
        public String periodEndDate;
        public String inActive;
        public List<CollectionPlanLineProduct> poductItems;
        public CollectionPlanLine(){
            poductItems = new List<CollectionPlanLineProduct>();
        }

    }
    /********************************************************************
    * Purpose: 付款计划明细的内部类
    *********************************************************************/
    public class CollectionPlanLineProduct{
        public String id;
        public String productName;
        public String productSapNumber;
        public String startDate;
        public String endDate;
        public String daysCount;

    }
    
    
}