public with sharing class SyncAccountSaleStatusToSapQueue implements Queueable {
    private Map<Id,String> AccountMap;
    private Set<Id> contractIds;
    public SyncAccountSaleStatusToSapQueue(Map<Id,String> AccountMap) {
        this.AccountMap = AccountMap;
    }

    public void execute(QueueableContext context) {
        try{
            Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(AccountMap);
            //异步任务执行日志
            CWUtility.createQueueLog('SyncAccountSaleStatusToSapQueue','Success',AccountMap.keySet(),'');
        }catch(Exception e){
            //异步任务执行日志
            CWUtility.createQueueLog('SyncAccountSaleStatusToSapQueue','Failed',AccountMap.keySet(),e.getMessage());
        }
    }
}