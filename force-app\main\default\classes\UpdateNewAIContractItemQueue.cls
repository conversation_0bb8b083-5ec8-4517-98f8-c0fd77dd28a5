public class UpdateNewAIContractItemQueue implements Queueable {
    Set<Id> productIds = new Set<Id>();
    Map<id,Set<Id>> contractidToProductidsMap = new Map<id,Set<Id>>();
    // 合同状态集合
    Set<String> contractStatusSet = new Set<String>{'InProgress','AutoRenewal','Amendment​'};
    public UpdateNewAIContractItemQueue(Map<id,Set<Id>> contractidToProductidsMap) {
        //this.productIds = productIds;
        this.contractidToProductidsMap = contractidToProductidsMap;
    }

    public void execute(QueueableContext context) {
        system.debug('UpdateNewAIContractItemQueue execute'+contractidToProductidsMap);
        List<Contract> updateContracts = new List<Contract>();
        List<Contract> contracts =  [SELECT id,Product_Category__c,Service_End__c,(Select id,ContractLineItemNo__c from Contract_Products__r) FROM Contract WHERE Product_Category__c ='P2' and SubscribeNewProducts__c = true and Contract_Status__c IN:contractStatusSet and id IN:contractidToProductidsMap.keySet()];
        system.debug('UpdateNewAIContractItemQueue contracts'+contracts);
        if(contracts == null || contracts.isEmpty()) return;
        User currentUser = [SELECT Id,profile.Name FROM User WHERE Id =:UserInfo.getUserId()];
        if( currentUser == null || ( !currentUser.profile.Name.contains('System Administrator') && !currentUser.profile.Name.contains('Integrations') && !currentUser.profile.Name.contains('API')))return;//仅限API接口创建的数据
        system.debug('UpdateNewAIContractItemQueue currentUser'+currentUser);
        for(Id conid:contractidToProductidsMap.keySet()){
            productIds.addAll(contractidToProductidsMap.get(conid));
        }
        //找最大的行项目号
        map<id,Integer> maxContractLineNoMap = new map<id,Integer>();
        for(Contract conTemp:contracts){
            Integer i= 0;
            for(Contract_Product__c conProduct:conTemp.Contract_Products__r){
                if(conProduct.ContractLineItemNo__c != null && i < Integer.valueOf(conProduct.ContractLineItemNo__c)){
                    i = Integer.valueOf(conProduct.ContractLineItemNo__c);
                }
            }
            maxContractLineNoMap.put(conTemp.Id,i);
        }

        List<Contract_Product__c> newAIproductList = new List<Contract_Product__c>();
        Map<id,Product2> newAIproductMap =new Map<id,Product2>([SELECT Id,Createddate FROM Product2 WHERE Id IN:productIds]);
        for(Contract con:contracts){
            system.debug('contractidToProductidsMap.containsKey(con.Id)'+contractidToProductidsMap.containsKey(con.Id));
            if(!contractidToProductidsMap.containsKey(con.Id) ) continue;
            //con.AddNewAIProducts__c = true;
            for(Id newAI:contractidToProductidsMap.get(con.Id)){
                Contract_Product__c newAIItem = new Contract_Product__c();
                newAIItem.Contract__c = con.Id;
                newAIItem.Product__c = newAI;
                newAIItem.ValidFrom__c = newAIproductMap.containsKey(newAI)?newAIproductMap.get(newAI).Createddate.date():null;
                newAIItem.ValidTo__c = con.Service_End__c;
                newAIItem.ContractLineItemNo__c = maxContractLineNoMap.containsKey(con.Id)? String.valueOf(maxContractLineNoMap.get(con.Id)+10):'';
                newAIproductList.add(newAIItem);
            }
            //updateContracts.add(con); 
        }
        system.debug('newAIproductList'+newAIproductList);
        if(!newAIproductList.isEmpty()){
            try {
                insert newAIproductList;
                //update updateContracts;
                CWUtility.createQueueLog('UpdateNewAIContractItemQueue','Success',contractidToProductidsMap.keySet(),'');
            }catch (Exception e) {
                CWUtility.createQueueLog('UpdateNewAIContractItemQueue','Failed',contractidToProductidsMap.keySet(),e.getMessage());
            }
        }

    }
}