public with sharing class BillPaymentAdviceTriggerHandler extends Trigger<PERSON>andler {
    public BillPaymentAdviceTriggerHandler() {
        super('BillPaymentAdvice__c');
    }

    public override void doAfterInsert(List<SObject> newList) {
        List<BillPaymentAdvice__c> newBillPaymentAdviceList = (List<BillPaymentAdvice__c>)newList;
        SetQueueSyncPayment(newBillPaymentAdviceList);
    }

    public void SetQueueSyncPayment(List<BillPaymentAdvice__c> newBillPaymentAdviceList){
        Set<id> needSyncSet = new Set<id>();
        for(BillPaymentAdvice__c bpa : newBillPaymentAdviceList){
            if(bpa.needSyncToSap__c ){
                needSyncSet.add(bpa.id);
            }
        }

        if(!needSyncSet.isEmpty()){ //system.enqueueJob(new SyncPaymentToSAPQueue(needSyncSet));
            if(needSyncSet.size()>20){
                CWUtility.createQueueLog('SyncPaymentToSAPQueue','Failed',needSyncSet,'needSyncSet more than 20');
            }else{
                for(Id paymentId : needSyncSet){
                    Interface_CRMSyncPaymentToSAP.doSyncPaymentToSAP(paymentId);
                }
            }
            
        } 
    }
}