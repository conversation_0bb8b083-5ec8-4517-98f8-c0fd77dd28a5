/**
 * Author: Dean
 * Date: 2025-07-09
 * Description: 合同触发器处理类
 * 1. 合同插入或更新时，若法务或产品经理填写了特殊备注，则自动将“特殊备注已填写”字段设为true，否则为false
 * 2. 合同审批通过后，将客户的销售状态更新为商用（03）并同步给SAP
 * 3. 合同审批通过后自动生成合同唯一编号和追溯码
 * 4. 合同审批通过后，自动发起队列任务创建收款计划
 * 5. 合同相关审批人自动分配
 * 6. 合同创建后根据所有人自动带出所在区域
 * 7. 合同状态变更时校验状态流转合法性
 * 
 * 测试类: ContractTriggerHandlerTest
 * 变更记录:
 * 2025-07-09: 创建
 */
public without sharing class ContractTriggerHandler extends TriggerHandler {
    public static String CONTRACTRENEW_RECORDTYPEID;
    public static String CONTRACTAMENDMENT_RECORDTYPEID;
    public static String CONTRACTNEW_RECORDTYPEID;

    public static String DRAFT = 'Draft';
    public static String APPROVING = 'ApprovalPending';
    public static String APPROVED = 'Approved';
    public static String REJECTED = 'Rejected';
    public static String SENT_TO_CUSTOMER = 'SentToClient';
    public static String SIGNED = 'ContractSigned';
    public static String NOT_SIGNED = 'ContractNotSigned';
    public static Boolean isAdmin = false;

    Map<String, String> typeConvertMap = new Map<String, String>{
        'P1' => 'Z001',
        'P2' => 'Z002',
        'P3' => 'Z003',
        'P4' => 'Z004',
        'P5' => 'Z005'
    };
    Map<String, String> regionConvertMap = new Map<String, String>{
        'Z0' => 'ALL',
        'Z1' => 'HD',
        'Z2' => 'HB',
        'Z3' => 'HN',
        'Z4' => 'HX',
        'Z5' => 'HW'
    };
    Map<String,String> statusConvertMap =  new Map<String,String>{
        'Draft' => 'Draft',
        'ApprovalPending' => 'In Approval Process',
        'Approved' => 'Activated',
        'Rejected' => 'Draft',
        'SentToClient' => 'Activated',
        'ContractSigned' => 'Activated',
        'ContractNotSigned' => 'Draft'
    };

    // 状态流转Map
    private Map<String, Set<String>> statusTransitionMap = new Map<String, Set<String>>{
        APPROVED.trim() => new Set<String>{SENT_TO_CUSTOMER.trim(),SIGNED.trim(),NOT_SIGNED.trim()},
        SENT_TO_CUSTOMER.trim() => new Set<String>{SIGNED.trim(),NOT_SIGNED.trim()},
        SIGNED.trim() => new Set<String>(),
        NOT_SIGNED.trim() => new Set<String>()
    };

    public ContractTriggerHandler() {
        super('Contract');
        if(CONTRACTRENEW_RECORDTYPEID == null)CONTRACTRENEW_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractRenew').getRecordTypeId();
        if(CONTRACTAMENDMENT_RECORDTYPEID == null)CONTRACTAMENDMENT_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('ContractAmendment').getRecordTypeId();
        if(CONTRACTNEW_RECORDTYPEID == null) CONTRACTNEW_RECORDTYPEID = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('NewContract').getRecordTypeId();
        String profileName = [SELECT Name FROM Profile WHERE Id =: UserInfo.getProfileId() LIMIT 1].Name;
        if(profileName == 'System Administrator' || profileName == '系统管理员')isAdmin = true;
    }

    public override void doBeforeInsert(List<SObject> newList) {
        List<Contract> newContracts = (List<Contract>)newList;
        SetAutoFullfilledFields(newContracts);
        SetContractRegion(newContracts);
        SetContractApprover(newContracts);
    }

    public override void doBeforeUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Contract> newContracts = (List<Contract>)newList;
        Map<Id, Contract> oldContracts = (Map<Id, Contract>)oldMap;
        SetAutoFullfilledFields(newContracts);
        SetContractCode(newContracts,oldContracts);
        SetContractTraceabilityCode(newContracts);
        ContarctChangeValidation(newContracts,oldContracts);
    }

    public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Contract> newContracts = (List<Contract>)newList;
        Map<Id, Contract> oldContracts = (Map<Id, Contract>)oldMap;
        Set<Id> contractIds = new Set<Id>();
        for(Contract newCon:newContracts){
            Contract oldCon = oldContracts.get(newCon.Id);
            if(newCon.Contract_Stage__c != oldCon.Contract_Stage__c && newCon.Contract_Stage__c == 'Approved'){
                contractIds.add(newCon.Id);
            }
        }
        if(contractIds.size() > 0){
            System.enqueueJob(new CreatePaymentPlanQueue(contractIds));
            handleContractSalesStatus(newContracts, oldContracts);
        }
    }

    //合同审批通过后，将客户的销售状态更新为商用（03）并同步给SAP
    private void handleContractSalesStatus(List<Contract> newContracts, Map<Id, Contract> oldContracts) {
        Set<Id> accountIdsToProcess = new Set<Id>();
        Map<Id, Contract> approvedContracts = new Map<Id, Contract>();

        // 只处理审批通过的合同
        for(Contract newCon : newContracts){
            Contract oldCon = oldContracts.get(newCon.Id);
            if(newCon.Contract_Stage__c != oldCon.Contract_Stage__c && newCon.Contract_Stage__c == 'Approved'){
                if(newCon.AccountId != null){
                    accountIdsToProcess.add(newCon.AccountId);
                    approvedContracts.put(newCon.Id, newCon);
                }
            }
        }
        if(accountIdsToProcess.isEmpty()) return;

        // 查询Account及其销售状态
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, SAP_Num__c,
            (SELECT Id, Product_Line__c, Sales_Status__c FROM ChildAccounts__r)
            FROM Account
            WHERE Id IN :accountIdsToProcess
        ]);

        List<Sales_Status__c> salesStatusToUpdate = new List<Sales_Status__c>();
        Map<Id, String> createAccountIdMap = new Map<Id, String>();

        for(Contract contract : approvedContracts.values()){
            Account acc = accountMap.get(contract.AccountId);
            String productLine = contract.Product_Category__c; 
            Boolean found = false;

            if(acc != null && acc.ChildAccounts__r != null && acc.ChildAccounts__r.size() > 0){
                for(Sales_Status__c status : acc.ChildAccounts__r){
                    if(status.Product_Line__c == productLine  && status.Sales_Status__c != '03'){
                        status.Sales_Status__c = '03';
                        salesStatusToUpdate.add(status);
                        found = true;
                    }
                }
            }

            // 只在有销售状态记录时才同步
            if(found && acc != null){
                createAccountIdMap.put(acc.Id, productLine);
            }
        }
        // 批量更新销售状态记录
        if(!salesStatusToUpdate.isEmpty()){
            update salesStatusToUpdate;
        }

        if(createAccountIdMap.keySet().size() > 0){
            system.enqueueJob(new SyncAccountSaleStatusToSapQueue(createAccountIdMap));
        }
    }

    //自动带出值的赋值逻辑
    public void SetAutoFullfilledFields(List<Contract> contracts){
        Map<id,Account> accountMap = new Map<id,Account>([SELECT Id,Customer_SimpleName__c from Account Where Customer_Category__c = 'C10']);

        for (Contract contract : contracts) {
            //审批的时候，若法务或产品经理填写了特殊备注，则特殊备注字段为true
            if (contract.Special_Notes__c != null && contract.Special_Notes__c != '') {
                contract.SpecialNotesFilled__c = true;
            }else{
                contract.SpecialNotesFilled__c = false;
            }
            //分销渠道
            if(contract.DistributionChannel__c == null && Trigger.isInsert){
                contract.DistributionChannel__c = 'D1';//默认是直销
            }
            //合同类型
            if( contract.Product_Category__c != null && typeConvertMap.containsKey(contract.Product_Category__c) ){
                contract.ContractType__c = typeConvertMap.get(contract.Product_Category__c);
            }
            //合同标准status字段
            if(statusConvertMap.containsKey(contract.Contract_Stage__c) && contract.Status != 'Activated'){
                contract.Status = statusConvertMap.get(contract.Contract_Stage__c);
            }
            //合同自定义状态字段
            if(contract.Contract_Stage__c == 'Draft') contract.Contract_Status__c = 'Draft';
            if(contract.Contract_Stage__c == 'Approval_Pending')contract.Contract_Status__c = 'InApprovalProcess';
            if(contract.Contract_Stage__c == 'Approved')contract.Contract_Status__c = 'InProgress';
            
            //合同销售组织
            if(accountMap.containsKey(contract.PartyB_Signing_Company__c)) contract.SalesOrganization__c = accountMap.get(contract.PartyB_Signing_Company__c).Customer_SimpleName__c;
        }
    }

    //合同审批通过后生成合同唯一编号
    public void SetContractCode(List<Contract> newContracts,Map<Id,Contract> oldContractsMap){
        map<String,ContractConfig__c> contractAutoNoMap = new map<String,ContractConfig__c>();
        for(ContractConfig__c conCode : [SELECT id,ExternalId__c,AutoNumber__c FROM ContractConfig__c WHERE DataType__c ='Config']){
            contractAutoNoMap.put(conCode.ExternalId__c, conCode);
        }

        for(Contract c:newContracts){
            Contract oldCon = oldContractsMap.get(c.id);
            if(oldCon.Contract_Stage__c != c.Contract_Stage__c && c.Contract_Stage__c == 'Approved'){
                if(c.Service_Start__c == null || c.Region__c== null || c.ContractAutoNo__c != null || !regionConvertMap.containsKey(c.Region__c)) continue;
                String key = 'SR' + String.valueOf(c.Service_Start__c.year())+ regionConvertMap.get(c.Region__c); 
                system.debug('key'+key);
                if(contractAutoNoMap.containsKey(key)){
                    ContractConfig__c currentConfig = contractAutoNoMap.get(key);
                    currentConfig.AutoNumber__c = currentConfig.AutoNumber__c + 1 ;
                    contractAutoNoMap.put(key,currentConfig);
                    c.ContractAutoNo__c = 'SR' + String.valueOf(c.Service_Start__c.year()) + String.valueOf(currentConfig.AutoNumber__c).leftPad(3, '0') + regionConvertMap.get(c.Region__c);
                }
            }
        }
        if(!contractAutoNoMap.isEmpty()) update contractAutoNoMap.values();
    }

    //合同审批通过后生成合同追溯码
    public void SetContractTraceabilityCode(List<Contract> newContracts){
        Set<id> oldContractIds = new Set<id>();
        for(Contract con:newContracts){
            if(con.OriginContract__c != null 
            && ( con.recordtypeid == CONTRACTRENEW_RECORDTYPEID || con.recordtypeid == CONTRACTAMENDMENT_RECORDTYPEID)){
                oldContractIds.add(con.OriginContract__c);
            }else if (con.OriginContract__c == null && con.recordtypeid == CONTRACTNEW_RECORDTYPEID ){
                con.ContractTraceabilityCode__c = con.ContractAutoNo__c + '-' + con.AcCompany_Code__c + '-'+ con.SalesOrganization__c;
            }
        }

        //基于老合同的变更的合同的流程
        if(oldContractIds.isEmpty()) return;
        Map<Id, Contract> oldContractMap = new Map<Id, Contract>([SELECT Id,recordtypeid,AcCompany_Code__c,ContractTypeFormula__c,SalesOrganization__c,ContractTraceabilityCode__c FROM Contract WHERE Id IN :oldContractIds]);
        system.debug('oldContractMap'+oldContractMap.values());
        for(Contract con:newContracts){
            if(con.OriginContract__c == null || !oldContractMap.containsKey(con.OriginContract__c))continue;
            Contract oldCon = oldContractMap.get(con.OriginContract__c);
            system.debug('oldCon'+oldCon);
            //拼接该合同的追溯码
            if(con.OriginContract__c != null && ( con.recordtypeid == CONTRACTRENEW_RECORDTYPEID || con.recordtypeid == CONTRACTAMENDMENT_RECORDTYPEID)){
                Boolean sameWithLastContract = true ;
                String appendCode = con.ContractTypeFormula__c ;
                if(oldcon.ContractTypeFormula__c != con.ContractTypeFormula__c && con.ContractTypeFormula__c != null){
                    sameWithLastContract = false;
                }
                if(oldCon.AcCompany_Code__c != con.AcCompany_Code__c && con.AcCompany_Code__c != null){
                    appendCode = appendCode + '-' +con.AcCompany_Code__c;
                    sameWithLastContract = false;
                }
                if(oldcon.SalesOrganization__c != con.SalesOrganization__c && con.SalesOrganization__c != null){
                    appendCode = appendCode + '-' +con.SalesOrganization__c;
                    sameWithLastContract = false;
                }

                if(oldCon.recordtypeid == CONTRACTNEW_RECORDTYPEID || !sameWithLastContract){
                    appendCode += '-01';
                    con.ContractTraceabilityCode__c = oldCon.ContractTraceabilityCode__c + '&' + appendCode;
                }else if(sameWithLastContract){
                    con.ContractTraceabilityCode__c = setVersionPlusTraceabilityCode(oldCon.ContractTraceabilityCode__c);
                }
            }
        }
    }

    //设置合同审批人
    public void SetContractApprover(List<Contract> contracts){
        List<User> approverList = [SELECT Id,Username,UserRole.Name,UserRole.DeveloperName,ProductLine__c FROM User WHERE isactive = true ]; 
        map<String,List<User>> approverMap = new map<String,List<User>>();
        map<String,List<User>> approverByProductMap = new map<String,List<User>>();
        Map<String,User> specificUserMap = new Map<String,User>();
        for(User u:approverList){
            if(!approverMap.containsKey(u.UserRole.DeveloperName)){
                approverMap.put(u.UserRole.DeveloperName,new List<User>());
            }
            approverMap.get(u.UserRole.DeveloperName).add(u);

            if(u.Username != null) {
                Integer idx = u.Username.indexOf('.com');
                if(idx > 0) {
                    String newUsername = u.Username.substring(0, idx + 4); // 保留到.com
                    specificUserMap.put(newUsername, u);
                } else {
                    specificUserMap.put(u.Username, u);
                }
            }
        }

        for(Contract con:contracts){
            //对应产品经理
            if(con.Product_Category__c == 'P1' && approverMap.containsKey('MSPManager'))con.ProductManagerApprover__c = approverMap.get('MSPManager')[0].id;
            if(con.Product_Category__c == 'P2' && approverMap.containsKey('MaaSManager'))con.ProductManagerApprover__c = approverMap.get('MaaSManager')[0].id;
            if(con.Product_Category__c == 'P3' && approverMap.containsKey('CulProductManager'))con.ProductManagerApprover__c = approverMap.get('CulProductManager')[0].id;
            if(con.Product_Category__c == 'P4' && approverMap.containsKey('AIInfrastructureManager'))con.ProductManagerApprover__c = approverMap.get('AIInfrastructureManager')[0].id;
            if(con.Product_Category__c == 'P5' && approverMap.containsKey('AI'))con.ProductManagerApprover__c = approverMap.get('AI')[0].id;
            if(con.Product_Category__c == 'P6' && approverMap.containsKey('Others'))con.ProductManagerApprover__c = approverMap.get('Others')[0].id;
            //区域经理
            if(con.Region__c == 'HD' && approverMap.containsKey('EastSalesManager'))con.RSMApprover__c = approverMap.get('EastSalesManager')[0].id;
            if(con.Region__c == 'HB' && approverMap.containsKey('NothSalesManager'))con.RSMApprover__c = approverMap.get('NothSalesManager')[0].id;
            if(con.Region__c == 'HN' && approverMap.containsKey('SouthSalesManager'))con.RSMApprover__c = approverMap.get('SouthSalesManager')[0].id;
            if(con.Region__c == 'HX' && approverMap.containsKey('WestSalesManager'))con.RSMApprover__c = approverMap.get('WestSalesManager')[0].id;
            if(con.Region__c == 'HW' && approverMap.containsKey('OverseasSalesManager'))con.RSMApprover__c = approverMap.get('OverseasSalesManager')[0].id;
            //产品总监
            if(con.Product_Category__c == 'P1' && approverMap.containsKey('MSPDirector'))con.ProductDirectorApprover__c = approverMap.get('MSPDirector')[0].id;
            if(con.Product_Category__c == 'P2' && approverMap.containsKey('MAASDirector'))con.ProductDirectorApprover__c = approverMap.get('MAASDirector')[0].id;
            if(con.Product_Category__c == 'P3' && approverMap.containsKey('Computing_Power'))con.ProductDirectorApprover__c = approverMap.get('Computing_Power')[0].id;
            if(con.Product_Category__c == 'P4' && approverMap.containsKey('AIInfrastructure'))con.ProductDirectorApprover__c = approverMap.get('AIInfrastructure')[0].id;
            if(con.Product_Category__c == 'P5' && approverMap.containsKey('AI_SearchDirector'))con.ProductDirectorApprover__c = approverMap.get('AI_SearchDirector')[0].id;
            if(con.Product_Category__c == 'P6' && approverMap.containsKey('Others'))con.ProductDirectorApprover__c = approverMap.get('Others')[0].id;
            //财务部经理
            if(approverMap.containsKey('FinanceDepartment'))con.FinanceManagerApprover__c = approverMap.get('FinanceDepartment')[0].id;
            //营销总监
            if(approverMap.containsKey('MarketingDep'))con.MarketingDirectorApprover__c = approverMap.get('MarketingDep')[0].id;
            //CEO
            if(approverMap.containsKey('CEO'))con.CEOApproval__c = approverMap.get('CEO')[0].id;
            //税务经理
            //客服经理
            if(con.Product_Category__c == 'P1' && specificUserMap.containsKey('<EMAIL>') )con.AccountManagerApprover__c = specificUserMap.get('<EMAIL>').id;
            if(con.Product_Category__c != 'P1' && specificUserMap.containsKey('<EMAIL>') )con.AccountManagerApprover__c = specificUserMap.get('<EMAIL>').id;
            //商务部经理
            if(con.Product_Category__c == 'P1' && specificUserMap.containsKey('<EMAIL>') )con.ComercialManagerApprover__c = specificUserMap.get('<EMAIL>').id;
            if(con.Product_Category__c != 'P1' && specificUserMap.containsKey('<EMAIL>') )con.ComercialManagerApprover__c = specificUserMap.get('<EMAIL>').id;
            
        }
    }

    //合同创建后根据Owner带出所在区域
    public void SetContractRegion(List<Contract> contracts){
        map<id,id> userMap = new map<id,id>();
        map<id,String> regionCodeMap = new Map<id,String>();
        for(Contract con:contracts){
            if(con.Region__c == null)userMap.put(con.id,con.ownerid);
        }
        for(User u:[Select id,SalesArea__c FROM user where id IN:userMap.values()]){
            if(u.SalesArea__c != null)regionCodeMap.put(u.id,u.SalesArea__c);
        }
        for(Contract conInsert:contracts){
            if(conInsert.Region__c == null && regionCodeMap.containsKey(conInsert.ownerid) ){
                if(regionCodeMap.containsKey(conInsert.ownerid)){
                    conInsert.Region__c = regionCodeMap.get(conInsert.ownerid);
                }else{
                    conInsert.Region__c.addError('无法根据合同所有人判断他所在的区域，请检查用户上的销售区域字段是否正确');
                }
            }
        }            
    }

    //合同状态更新验证
    public void ContarctChangeValidation(List<Contract> newContracts,Map<Id,Contract> oldContractsMap){
        if(isAdmin)return;
        for(Contract con:newContracts){
            Contract oldCon = oldContractsMap.get(con.id);
            if(con.Contract_Stage__c != oldCon.Contract_Stage__c){
                String oldStatus = oldCon.Contract_Stage__c.trim();
                String newStatus = con.Contract_Stage__c.trim();
                if (statusTransitionMap.containsKey(oldStatus)) {
                    Set<String> newStatusSet = statusTransitionMap.get(oldStatus);
                    Boolean isContain = false;
                    for(String status:newStatusSet){
                        if(status == newStatus){
                            isContain = true;
                        }
                    }
                    if(!isContain)con.Contract_Stage__c.addError('不能更新合同状态');
                }
            }
        }
    }

    //合同签署后锁定数据
    public void LockDataAfterContractSigned(List<Contract> newContracts,Map<Id,Contract> oldContractsMap){
        List<Id> contractIds = new List<Id>();
        for(Contract con:newContracts){
            Contract oldCon = oldContractsMap.get(con.id);
            if(con.Contract_Stage__c != oldCon.Contract_Stage__c && con.Contract_Stage__c == 'ContractSigned'){
                contractIds.add(con.id);
            }
        }
        if(contractIds.size() > 0) Approval.lock(contractIds);
    }

    //解析编码的最后一个版本号并加一
    public String setVersionPlusTraceabilityCode(String text ){
        List<String> parts = text.split('&');
        String lastPart = parts[parts.size() - 1];
        List<String> subParts = lastPart.split('-');
        String lastNumber = subParts[subParts.size() - 1];
        Integer lastDashIndex = text.lastIndexOf('-');
        String newTraceabilityCode;
        if (lastDashIndex != -1) newTraceabilityCode = text.substring(0, lastDashIndex + 1) + String.valueOf(Integer.valueOf(lastNumber) + 1).leftPad(2, '0');
        return newTraceabilityCode;
    }
}