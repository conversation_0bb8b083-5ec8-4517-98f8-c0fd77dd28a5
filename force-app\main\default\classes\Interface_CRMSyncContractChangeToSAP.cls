public without sharing class Interface_CRMSyncContractChangeToSAP {
    public Interface_OutboundParam param {get;set;}
    Map<String, Object> requestBody = new Map<String, Object>();
    public static String RENEW_RECORDTYPEID = Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('ContractRenew').getRecordTypeId();
    public static String TERMINATE_RECORDTYPEID = Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('ContractTermination').getRecordTypeId();
    public static String CUSTOMERID_RECORDTYPEID = Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('AccountIDChange').getRecordTypeId();
    public static String ADDPRODUCT_RECORDTYPEID = Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('NewAIProducts').getRecordTypeId();


    public Interface_CRMSyncContractChangeToSAP(Id contractChangeId) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncContractChangeToSAP';
        param.endpoint ='callout:SAP_API_Credential';
        param.retryTimes = 3;
        param.recordIdSet = new Set<id>{contractChangeId};
        param.targetObject = 'Contract_Change_Request__c';

        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(contractChangeId);
        Interface_OutboundExecutor syncContractChange = new Interface_OutboundExecutor(param);
        String responseBody = syncContractChange.execute();
    }

    private String getRequestHeader(){
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    private String getRequestBody(Id contractChangeId){
        Contract_Change_Request__c contractChange = [
            SELECT Id,RecordTypeId,Contract__c,Contract__r.SapContractNo__c,Contract__r.Contract_Status__c,NewEndDate__c,ContractOldEndDate__c,AIProductIDs__c,
            (SELECT id,MaterialNumber__c,ContarctProduct__c,ContarctProduct__r.Quantity__c,ContarctProduct__r.PriceUnit__c,ContarctProduct__r.NetPrice__c,
            ContarctProduct__r.TaxCode_formula__c,ContarctProduct__r.ValidFrom__c,ContarctProduct__r.ValidTo__c,ContarctProduct__r.MaterialNumber__c,ContractLineItemNo__c,CustomerAccountIDs__c 
            FROM Contract_Change_Request_Details__r) 
            FROM Contract_Change_Request__c WHERE id =:contractChangeId];
        
        String contractId = contractChange.Contract__c;
        String planListJson = CWUtility.getPlanJosn(contractId);
        if(contractChange != null ){//合同自动续约 / 合同终止 
            RequestBody requestBody = new RequestBody();
            ChangeRequest request = new ChangeRequest();
            if( contractChange.Contract__c == null || contractChange.Contract__r.SapContractNo__c == null || contractChange.Contract__r.SapContractNo__c == ''){
                CWUtility.createQueueLog('Interface_CRMSyncContractChangeToSAP','Failed',new Set<id>{contractChangeId},'合同调整单上的合同没有SAP号，无法同步SAP');
                return '';
            }
            request.VBELN = contractChange.Contract__r.SapContractNo__c;
            if(contractChange.RecordTypeId == RENEW_RECORDTYPEID){
                request.GUEEN = contractChange.NewEndDate__c == null ? '':DataProcessTool.formatSAPDate(contractChange.NewEndDate__c);
                request.ZTEXT = planListJson;
                request.GBSTK = '1';//进行中
            }else if(contractChange.RecordTypeId == TERMINATE_RECORDTYPEID){
                request.GUEEN = contractChange.NewEndDate__c == null ? '':DataProcessTool.formatSAPDate(contractChange.NewEndDate__c);
                system.debug('contractChange.NewEndDate__c'+contractChange.NewEndDate__c);
                system.debug('contractChange.ContractOldEndDate__c'+contractChange.ContractOldEndDate__c);
                if(contractChange.NewEndDate__c < contractChange.ContractOldEndDate__c){
                    request.ZTEXT = planListJson;
                    request.GBSTK = '2'; //提前终止
                }else if(contractChange.NewEndDate__c == contractChange.ContractOldEndDate__c){
                    request.GBSTK = '3';//到期终止（完成）
                }
            }else if(contractChange.RecordTypeId == CUSTOMERID_RECORDTYPEID && contractChange.Contract_Change_Request_Details__r.size()>0){
                List<ChangeRequestItem> childList = new List<ChangeRequestItem>();
                for(ContractChangeRequestDetail__c crd:contractChange.Contract_Change_Request_Details__r){
                    ChangeRequestItem customerIdChangeItem = new ChangeRequestItem();
                    customerIdChangeItem.ZITMES = crd.ContarctProduct__c;
                    customerIdChangeItem.POSNR = crd.ContractLineItemNo__c == null? '':crd.ContractLineItemNo__c;
                    customerIdChangeItem.ZTEXT1 = crd.CustomerAccountIDs__c == null? '':crd.CustomerAccountIDs__c;
                    childList.add(customerIdChangeItem);
                }
                request.item = childList;
            }else if(contractChange.RecordTypeId == ADDPRODUCT_RECORDTYPEID ){
                List<String> aiProductIdSet = contractChange.AIProductIDs__c.split(';');
                Contract changeContract = [Select id,(Select id,ContractLineItemNo__c,MaterialNumber__c,Quantity__c,Unit__c,NetPrice__c,TaxCode_formula__c,ValidFrom__c,ValidTo__c from Contract_Products__r where Product__c IN:aiProductIdSet) from Contract where id =:contractId];
                List<ChangeRequestItem> childList = new List<ChangeRequestItem>();
                for(Contract_Product__c crd:changeContract.Contract_Products__r){
                    ChangeRequestItem customerIdChangeItem = new ChangeRequestItem();
                    customerIdChangeItem.ZITMES = crd.id;
                    customerIdChangeItem.POSNR = crd.ContractLineItemNo__c == null? '':crd.ContractLineItemNo__c;

                    customerIdChangeItem.MATNR = crd.MaterialNumber__c == null? '':crd.MaterialNumber__c;
                    customerIdChangeItem.ZMENG = crd.Quantity__c == null? '':String.valueOf(crd.Quantity__c);
                    customerIdChangeItem.ZIEME = crd.Unit__c == null? '':crd.Unit__c;
                    customerIdChangeItem.NETPR = crd.NetPrice__c == null? '':String.valueOf(crd.NetPrice__c);
                    customerIdChangeItem.MWSK1 = crd.TaxCode_formula__c == null? '':crd.TaxCode_formula__c;
                    customerIdChangeItem.ZVLDF = crd.ValidFrom__c == null? '':DataProcessTool.formatSAPDate(crd.ValidFrom__c);
                    customerIdChangeItem.ZVLDT = crd.ValidTo__c == null? '':DataProcessTool.formatSAPDate(crd.ValidTo__c);
                    childList.add(customerIdChangeItem);
                }
                request.ZTEXT = planListJson;
                request.item = childList;
            }
            requestBody.req = request;
            String requestBodyJSON = JSON.serialize(requestBody);
            return requestBodyJSON;
        }else{
            CWUtility.createQueueLog('Interface_CRMSyncContractChangeToSAP','Failed',new Set<id>{contractChangeId},'找不到合同调整单，无法同步SAP');
            return '';
        }

    }

    public class RequestBody{
        public String UUID;//接口唯一标识
        public String ZNUMB;//接口编号
        public String FSYSID;//请求系统
        public ChangeRequest req;
        public RequestBody(){
            UUID = CWUtility.generateUUID();
            ZNUMB = 'SD010';
            FSYSID = 'SALESFORCE';
            req = new ChangeRequest();
        }
    }

    public class ChangeRequest{
        public String VBELN;//SAP合同编号
        public String GUEEN; //有效期至
        public String GBSTK;//合同状态
        public String ZTEXT;//收款计划
        public List<ChangeRequestItem> item;
        public ChangeRequest(){
            item = new List<ChangeRequestItem>();
        }
    }   

    public class ChangeRequestItem{
        public String ZITMES;//行项目唯一标识
        public String POSNR;//合同行项目
        public String MATNR;//物料编码
        public String ZMENG;//数量
        public String ZIEME;//计量单位
        public String NETPR;//净价
        public String MWSK1;//税码
        public String ZVLDF;//有效期自
        public String ZVLDT;//有效期至
        public String ZTEXT1;//客户ID
    }

}