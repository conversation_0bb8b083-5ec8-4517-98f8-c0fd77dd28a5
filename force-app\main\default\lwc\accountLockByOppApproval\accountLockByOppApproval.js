import { LightningElement, api, wire,track } from 'lwc';
import { getRecord } from 'lightning/uiRecordApi';
import HAS_ACTIVE_APPROVAL_FIELD from '@salesforce/schema/Account.Has_Active_Approval__c';

export default class AccountLockByOppApproval extends LightningElement {
    @api recordId; // 客户记录ID
    @track layoutType = 'Full'; // 默认布局
    @track isLoading = true;

    // 获取客户的Has_Active_Opproval__c字段值
    @wire(getRecord, { 
        recordId: '$recordId', 
        fields: [HAS_ACTIVE_APPROVAL_FIELD] 
    })
    wiredAccount({ error, data }) {
        this.isLoading = false;
        if (data) {
            const hasActiveApproval = data.fields.Has_Active_Opproval__c.value;
            // 根据标记字段切换布局：TRUE→只读布局，FALSE→正常布局
            this.layoutType = hasActiveApproval ? 'Approval_Lock_Layout' : 'Full';
        } else if (error) {
            console.error('获取客户信息失败:', error);
            this.layoutType = 'Full'; // 出错时默认显示正常布局
        }
    }
}