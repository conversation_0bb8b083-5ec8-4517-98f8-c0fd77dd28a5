import { LightningElement, api, wire, track } from 'lwc';
import { getRecord } from 'lightning/uiRecordApi';
import HAS_ACTIVE_APPROVAL_FIELD from '@salesforce/schema/Account.Has_Active_Approval__c';

export default class AccountLockByOppApproval extends LightningElement {
    @api recordId; // 客户记录ID
    @track layoutType = 'Full'; // 默认布局
    @track recordMode = 'edit'; // 记录模式：edit 或 view
    @track isLoading = true;

    // 获取客户的Has_Active_Approval__c字段值
    @wire(getRecord, {
        recordId: '$recordId',
        fields: [HAS_ACTIVE_APPROVAL_FIELD]
    })
    wiredAccount({ error, data }) {
        this.isLoading = false;
        if (data) {
            const hasActiveApproval = data.fields.Has_Active_Approval__c.value;
            // 根据Has_Active_Approval__c字段值切换布局和模式
            if (hasActiveApproval) {
                // 如果有活跃的审批，使用只读布局和只读模式
                this.layoutType = '00hF30000045SgkIAE'; // 或者使用您定义的只读布局名称
                this.recordMode = 'view';
            } else {
                // 如果没有活跃的审批，使用默认布局和编辑模式
                this.layoutType = 'Full';
                this.recordMode = 'edit';
            }
        } else if (error) {
            console.error('获取客户信息失败:', error);
            // 出错时默认显示可编辑布局
            this.layoutType = 'Full';
            this.recordMode = 'edit';
        }
    }
}