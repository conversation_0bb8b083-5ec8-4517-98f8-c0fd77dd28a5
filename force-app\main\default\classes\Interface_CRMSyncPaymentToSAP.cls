public without sharing class Interface_CRMSyncPaymentToSAP {
    public static Interface_OutboundParam param {get;set;}
    //public  static Map<String, Object> requestBody = new Map<String, Object>();

    @future(callout=true)
    public static void doSyncPaymentToSAP(String BillPaymentAdviceId) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncPaymentToSAP';
        param.endpoint ='callout:SAP_API_Credential';
        param.retryTimes = 3;
        param.recordIdSet = new Set<id>{BillPaymentAdviceId};
        param.targetObject = 'BillPaymentAdvice__c';
        
        //String token = '';
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(BillPaymentAdviceId);

        Interface_OutboundExecutor syncContract = new Interface_OutboundExecutor(param);
        String responseBody = syncContract.execute();
        system.debug('Response Body: ' + responseBody);
        // 解析SAP接口返回的数据
        if (String.isNotBlank(responseBody)) {
            try {
                // 假设返回为JSON格式
                Map<String, Object> respMap = (Map<String, Object>) JSON.deserializeUntyped(responseBody);
                String msgType = (respMap.containsKey('msgty') && respMap.get('msgty') != null) ? String.valueOf(respMap.get('msgty')) : '';
                String msgText = (respMap.containsKey('msgtx') && respMap.get('msgtx') != null) ? String.valueOf(respMap.get('msgtx')) : '';

                String returnMsg = (respMap.containsKey('field1') && respMap.get('field1') != null) ? String.valueOf(respMap.get('field1')): ''; 
                Map<String, Object> field1 =  (Map<String, Object>) JSON.deserializeUntyped(returnMsg);
                system.debug('field1'+field1);
                
                if(msgType.toUpperCase() =='S' && field1 != null){
                    system.debug('field12'+field1);
                    BillPaymentAdvice__c updateObj = new BillPaymentAdvice__c();
                    String belnr = (field1.containsKey('belnr') && field1.get('belnr') != null) ? String.valueOf(field1.get('belnr')) : '';
                    String bukrs = (field1.containsKey('bukrs') && field1.get('bukrs') != null) ? String.valueOf(field1.get('bukrs')) : '';
                    String gjahr = (field1.containsKey('gjahr') && field1.get('gjahr') != null) ? String.valueOf(field1.get('gjahr')) : '';
                    String monat = (field1.containsKey('monat') && field1.get('monat') != null) ? String.valueOf(field1.get('monat')) : '';
                    updateObj.id = BillPaymentAdviceId;
                    updateObj.Financial_Number__c = belnr;
                    updateObj.Financial_Year__c = gjahr;
                    updateObj.Financial_Month__c = monat;

                    if(belnr != '' && bukrs != '' && gjahr != '' && monat != ''){
                        system.debug('field123'+updateObj);
                        update updateObj;
                    }
                }

            } catch (Exception e) {
                CWUtility.createQueueLog('Interface_CRMSyncPaymentToSAP','Failed',new Set<Id>{BillPaymentAdviceId},'解析失败');
            }
        }
    } 

    public Interface_CRMSyncPaymentToSAP() { 
    }

    public static String getRequestHeader(){
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        //headerMap.put('sap-client', '100');
        //headerMap.put('Authorization', 'Bearer '+token);
        //headerMap.put('Authorization', 'Basic ' + encodedCredentials);
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    public static String getRequestBody(Id BillPaymentAdviceId){
        List<BillPaymentAdvice__c> BillPaymentAdviceList = [
            SELECT Id, OwnerId, Name, CurrencyIsoCode, RecordTypeId, CreatedDate, RecordType.Name,RecordType.developerName,
                   Billing_Number__c, Account_Number__c, Account__c, Billing_Date__c, Contract_Currency__c,SAP_Num__c,
                   Exchange_Rate__c, Contract_Number__c, SAP_Contract_Number__c, 
                   Total_Tax_Amount__c,
                   Total_Amount__c,
                   Collection_Plan_Line__c, Company_Code__c, Account__r.Name, AccountBankName__c, BankAccount__c, Payment_Currency__c,
                   Billing_Type__c, Creat_Date__c, 
                   PayableAmount__c, Adjustment_Note__c 
            FROM BillPaymentAdvice__c 
            WHERE Id =:BillPaymentAdviceId];

        list<PaymentRequest> returnList = new list<PaymentRequest>();
        RequestBody body = new RequestBody();
        for(BillPaymentAdvice__c con : BillPaymentAdviceList){
            PaymentRequest pr = new PaymentRequest();
            pr.BUKRS = con.Company_Code__c;//公司代码

            pr.BUDAT = DataProcessTool.formatSAPDate(con.CreatedDate.date()); // 转换 DateTime 为 Date 类型
            pr.BLDAT = DataProcessTool.formatSAPDate(con.CreatedDate.date()); // 转换 DateTime 为 Date 类型

            // 获取月份并确保是两位数格式
            Integer monthNum = con.CreatedDate.month();
            pr.MONAT = monthNum < 10 ? '0' + String.valueOf(monthNum) : String.valueOf(monthNum);
            
            pr.WAERS = con.Payment_Currency__c;//动态值，账单的结算币种
            String paymentType = con.RecordType.developerName =='PrepaymentAdvice' ? 'A' : 'H';
            Decimal totalAmount = con.Total_Amount__c==null?0:con.Total_Amount__c;
            Decimal totalTaxAmount = con.Total_Tax_Amount__c==null?0:con.Total_Tax_Amount__c;
            String tAmount = String.valueOf(totalAmount+totalTaxAmount);
            pr.item.add(new PaymentItem('50','002','H',tAmount,'','','1001010000','207'));
            pr.item.add(new PaymentItem('09','001','S',tAmount,paymentType,con.SAP_Num__c,'',''));
            body.req = pr;
        }
        String reqString = JSON.serialize(body);
        system.debug(reqString);
        return reqString;
    }
    public class RequestBody{
        public String UUID;//接口唯一标识
        public String ZNUMB;//接口编号
        public String fsysid;//请求系统
        public PaymentRequest req;
        public RequestBody(){
            UUID = CWUtility.generateUUID();
            ZNUMB = 'FI006';
            fsysid = 'POT';
            req = new PaymentRequest();
        }
    }

    public class PaymentRequest {
        public String BSTAT = ''; // 固定值，传空
        public String BELNR = ''; // 固定值，传空
        public String LDGRP = ''; // 固定值，传空
        public String BUKRS ; // 动态值，基于每个销售组织代码
        public String BLART = 'SA'; // 固定值，SA
        public String BUDAT ; // 动态值，创建账单的日期
        public String BLDAT ; // 动态值，创建账单的日期
        public String MONAT ; // 动态值，对应账单日期的月份
        public String WAERS ; // 动态值，账单的结算币种
        public String BKTXT = ''; // 固定值，传空
        public String XBLNR = 'Salesforce'; // 固定值，传Salesforce
        public String XREF2 = ''; // 固定值，为空
        public List<PaymentItem> item;
        public PaymentRequest(){
            item = new List<PaymentItem>();
        }
    } 

    public class PaymentItem {
        public String TYPE = 'AR'; // 类型
        public String BSCHL; // 过账码
        public String DOCLN; // 行项目号
        public String SHKZG; // 借/贷标识
        public String ACCOUNT ; // "供应商/客户号/总账科目"
        public String RACCT ; // 总账科目（备选统驭科目）
        public String UMSKZ; // 特别总账标志，预收A。押金H
        public String TSL; // 凭证货币金额
        public String RSTGR = ''; // 原因代码
        public String RCNTR = ''; // 成本中心
        public String PRCTR = ''; // 利润中心
        public String OBJNR = ''; // 内部订单
        public String PROJK = ''; // wbs要素
        public String XNEGP = ''; // 反记账标识
        public String MWSKZ = ''; // 税码
        public String RASSC = ''; // 贸易伙伴
        public String ZTERM = ''; // 付款条款
        public String ZFBDT = ''; // 基准日期
        public String RFAREA = ''; // 功能范围
        public String ZUONR = ''; // 分配
        public String SGTXT = ''; // 行项目文本
        public String BANK = ''; // 出票银行代码
        public String ACCOU = ''; // 银行账户
        public PaymentItem(String BSCHL, String DOCLN, String SHKZG,String TSL,String UMSKZ,String ACCOUNT,String RACCT,String RSTGR) {
            this.BSCHL = BSCHL;
            this.DOCLN = DOCLN;
            this.SHKZG = SHKZG;
            this.TSL = TSL;
            this.UMSKZ = UMSKZ;
            this.ACCOUNT = ACCOUNT;
            this.RACCT = RACCT;
            this.RSTGR = RSTGR;
        }
    }


}