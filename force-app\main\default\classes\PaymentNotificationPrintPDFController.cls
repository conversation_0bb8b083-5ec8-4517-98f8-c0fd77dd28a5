public without sharing class PaymentNotificationPrintPDFController {
    @AuraEnabled
    public static String generatePdf(Id recordId) {
        try {
            // 生成PDF内容
            BillPaymentAdvice__c paymentRecord = [SELECT Id, PartyB_Signing_Company__r.Customer_SimpleName__c,Account__r.ProvinceId__r.UniqueCode__c FROM BillPaymentAdvice__c WHERE Id=:recordId];
            PageReference pdfPage =Page.Payment_MasterLandPDF;//默认境内客户
            String hongkongStr = System.Label.HongKongOrg;
            String internalStr = System.Label.Internal_Org;
            String SGStr = System.Label.SG_Org;
            List<String> hongkongList = hongkongStr.split(';');
            List<String> internalList = internalStr.split(';');
            List<String> SGList = SGStr.split(';');
            if (hongkongList.contains(paymentRecord.PartyB_Signing_Company__r.Customer_SimpleName__c)) {
                pdfPage = Page.Payment_ServiceInvoicePDF;
            }
            if (SGList.contains(paymentRecord.PartyB_Signing_Company__r.Customer_SimpleName__c)) {
                pdfPage = Page.Payment_TaxInvoicePDF;
            }
            pdfPage.getParameters().put('id', recordId);
            Blob pdfBlob;
            
            if (Test.isRunningTest()) {
                // 测试环境下模拟PDF内容
                pdfBlob = Blob.valueOf('Test PDF Content');
            } else {
                pdfBlob = pdfPage.getContentAsPDF();
            }
            //删除原本的PDF文件
            Database.delete([SELECT ContentDocumentId FROM ContentDocumentLink WHERE LinkedEntityId = :recordId]);
            // 保存为ContentVersion
            ContentVersion cv = new ContentVersion(
                Title = '付款通知书' + DateTime.now().format('yyyyMMddHHmmss'),
                PathOnClient = 'payment.pdf',
                VersionData = pdfBlob,
                FirstPublishLocationId = recordId
            );
            insert cv;
            
            // 返回ContentDocumentId以便下载
            cv = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];
            return cv.ContentDocumentId;
            
        } catch (Exception e) {
            throw new AuraHandledException('生成PDF时出错: ' + e.getMessage());
        }
    }
}