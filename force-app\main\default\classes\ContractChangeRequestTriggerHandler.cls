/**
 * Author: Dean
 * Date: 2025-07-09
 * Description: 合同调整单触发器处理类
 * 1.当合同调整单审批通过后，更新合同信息
 * 2.获取合同上的信息
 * 
 * Test Class: ContractChangeRequestTriggerHandlerTest
 * Change Log:
 * 2025-07-09: Created
 */
public without sharing class ContractChangeRequestTriggerHandler extends TriggerHandler{
    public ContractChangeRequestTriggerHandler() {
        super('Contract_Change_Request__c');
    }

    public override void doBeforeInsert(List<SObject> newList) {
        List<Contract_Change_Request__c> newRequests = (List<Contract_Change_Request__c>)newList;
        getOriginContractInfo(newRequests);
        getApprovers(newRequests);
        
    }

    public override void doAfterInsert(List<SObject> newList){
        List<Contract_Change_Request__c> newRequests = (List<Contract_Change_Request__c>)newList;
        preGenerateContractItem(newRequests);
        updateContractAfterApproved(newRequests,null);
    }

    public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        List<Contract_Change_Request__c> newRequests = (List<Contract_Change_Request__c>)newList;
        Map<Id, Contract_Change_Request__c> oldRequests = (Map<Id, Contract_Change_Request__c>)oldMap;
        updateContractAfterApproved(newRequests, oldRequests);
    }

    //当合同调整单审批通过后，更新合同信息
    private void updateContractAfterApproved(List<Contract_Change_Request__c> newRequests, Map<Id, Contract_Change_Request__c> oldRequests) {
        system.debug('updateContractAfterApproved');
        boolean needUpdateContractItem = false;
        boolean needAddNewAIProduct = false;
        boolean needUpdatePaymentPlan = false;
        Set<Id> ccrIds = new Set<Id>();
        Set<Id> ccrContractIds = new Set<Id>();
        Set<Id> needupdatePlanIds = new Set<Id>();
        List<Contract> updateContracts = new List<Contract>();
        map<id,Set<Id>> aiProductIdsContractMap = new map<id,Set<Id>>();
        for(Contract_Change_Request__c ccr:newRequests){
            if(oldRequests == null){//AI服务模型产品
                if(ccr.ApprovalStatus__c == 'Approved' && ccr.Contract__c != null && ccr.RecordTypeNameForApprove__c == 'NewAIProducts'){
                    needAddNewAIProduct = true;
                    needUpdatePaymentPlan = true;
                    needupdatePlanIds.add(ccr.id);
                    Set<Id> aiProductIdSet = new Set<Id>();
                    if(ccr.AIProductIDs__c != null ){
                        for(String str:ccr.AIProductIDs__c.split(';')){
                            aiProductIdSet.add(str);
                        }
                    }
                    aiProductIdsContractMap.put(ccr.Contract__c,aiProductIdSet);
                }
            }else{
                Contract_Change_Request__c oldccr = oldRequests.get(ccr.Id);
                if(ccr.ApprovalStatus__c != oldccr.ApprovalStatus__c && ccr.ApprovalStatus__c == 'Approved' && ccr.Contract__c != null){
                    if(ccr.RecordTypeNameForApprove__c == 'ContractTermination'){
                        if(ccr.ContractOldEndDate__c > ccr.NewEndDate__c ){//提前终止
                            needUpdatePaymentPlan = true;
                            needupdatePlanIds.add(ccr.id);
                            Contract con = new Contract(Id=ccr.Contract__c, Service_End__c = ccr.NewEndDate__c,Contract_Status__c='Termination');
                            system.debug('con:'+con);
                            updateContracts.add(con);
                        }else{//到期终止（合同完成）
                            Contract con = new Contract(Id=ccr.Contract__c, Service_End__c = ccr.NewEndDate__c,Contract_Status__c='Completed');
                            updateContracts.add(con);
                        }
                    }else if(ccr.RecordTypeNameForApprove__c == 'ContractRenew'){//自动续约
                        needUpdatePaymentPlan = true;
                        needupdatePlanIds.add(ccr.id);
                        Contract con = new Contract(Id=ccr.Contract__c, Service_End__c = ccr.NewEndDate__c,Contract_Status__c='​​AutoRenewal​');
                        updateContracts.add(con);
                    }else if(ccr.RecordTypeNameForApprove__c == 'AccountIDChange'){//账号ID调整
                        needUpdateContractItem = true;
                        ccrIds.add(ccr.id);
                    }
                }
            }
            
        }
        if(updateContracts.size() > 0) update updateContracts;

        if(needUpdateContractItem){
            map<Id ,String> contractChangeItemMap = new Map<Id,String>();
            List<Contract_Product__c> contractProductList = new List<Contract_Product__c>();
            for(ContractChangeRequestDetail__c ccrItem:[SELECT Id, Product__c,ContarctProduct__c, ContractChangeRequest__r.Contract__c,CustomerAccountIDs__c
                FROM ContractChangeRequestDetail__c
                WHERE ContractChangeRequest__c IN:ccrIds]){
                    contractChangeItemMap.put(ccrItem.ContarctProduct__c,ccrItem.CustomerAccountIDs__c);
            }

            for(Contract_Product__c contractItem :[
                SELECT Id,CustomerIDs__c
                FROM Contract_Product__c
                WHERE id IN :contractChangeItemMap.keySet()
            ]){
                if(contractChangeItemMap.containsKey(contractItem.id)){
                    contractItem.CustomerIDs__c = contractChangeItemMap.get(contractItem.id);
                    contractProductList.add(contractItem);
                }
            }

            if(contractProductList.size() > 0 ){
                update contractProductList;
            }
        }
        system.debug('needAddNewAIProduct:'+needAddNewAIProduct);
        if(needAddNewAIProduct){
            system.debug('aiProductIdsContractMap:'+aiProductIdsContractMap);
            system.enqueueJob(new UpdateNewAIContractItemQueue(aiProductIdsContractMap));
        }
        if(needUpdatePaymentPlan){
            system.debug('needUpdatePaymentPlan:'+needupdatePlanIds);
            system.enqueueJob(new UpdatePaymentPlanQueue(needupdatePlanIds));
        }

    }

    //获取老合同上的信息
    private void getOriginContractInfo(List<Contract_Change_Request__c> newRequests) {
        Set<Id> contractIds = new Set<Id>();
        map<id,Contract> contractMap;
        for(Contract_Change_Request__c ccr:newRequests){
            contractIds.add(ccr.Contract__c);
        }

        if(!contractIds.isEmpty()){
            contractMap = new Map<id,Contract>([SELECT id,Service_End__c FROM Contract WHERE id in:contractIds]);
        }

        for(Contract_Change_Request__c newObj:newRequests){
            if(contractMap.containsKey(newObj.Contract__c)){
                newObj.ContractOldEndDate__c =  contractMap.get(newObj.Contract__c).Service_End__c;
            }
        }

    }

    //获取合同上的审批人
    private void getApprovers(List<Contract_Change_Request__c> newRequests) {
        Set<Id> contractIds = new Set<Id>();

        for(Contract_Change_Request__c ccr:newRequests){
            if(ccr.Contract__c  != null){
                contractIds.add(ccr.Contract__c);
            }
        }
        Map<id,Contract> contractMap = new Map<id,Contract>([SELECT id,TotalAmount__c,CEOApproval__c,ComercialManagerApprover__c,ProductDirectorApprover__c,ProductManagerApprover__c,RSMApprover__c,AccountManagerApprover__c,TaxManagerApprover__c,MarketingDirectorApprover__c,FinanceManagerApprover__c FROM Contract WHERE id in:contractIds]);
        for(Contract_Change_Request__c ccr:newRequests){
            Contract con = contractMap.containsKey(ccr.Contract__c)?contractMap.get(ccr.Contract__c):null;
            if(con == null) continue;
            ccr.CEOApprover__c = con.CEOApproval__c;
            ccr.ProductManagerApprover__c = con.ProductManagerApprover__c;
            ccr.ProductDirectorApprover__c = con.ProductDirectorApprover__c;
            ccr.CustomerServiceManagerApprover__c = con.AccountManagerApprover__c;
            ccr.MarketingDirectorApprover__c = con.MarketingDirectorApprover__c;
            ccr.FinanceApprover__c = con.FinanceManagerApprover__c;
            ccr.RSMApprover__c = con.RSMApprover__c;
            ccr.TaxManagerApprover__c = con.TaxManagerApprover__c;
            ccr.CommercialManagerApprover__c = con.ComercialManagerApprover__c;
            ccr.TotalAmount__c = con.TotalAmount__c;
        }
    }

    //若是账户ID调整的，预生成调整单明细数据
    private void preGenerateContractItem(List<Contract_Change_Request__c> newRequests) {
        Map<id,id> contractIdToccrMap = new Map<id,id>();
        List<ContractChangeRequestDetail__c> insertList = new List<ContractChangeRequestDetail__c>();
        for(Contract_Change_Request__c ccr:newRequests){
            if(ccr.RecordTypeId == Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('AccountIDChange').getRecordTypeId()){
                contractIdToccrMap.put(ccr.Contract__c,ccr.id);
            }
        }

        //验证是否可以创建 客户ID调整 的合同调整单
        List<Contract_Product__c> productList =[SELECT id,Contract__c,CustomerIDs__c FROM Contract_Product__c WHERE Contract__c in:contractIdToccrMap.keySet() and shouldhaveCustomerIDs__c = true];
        Set<id> contractIdSet = new  Set<id>();
        for(Contract_Product__c pro:productList){
            contractIdSet.add(pro.Contract__c);
        }
        for(Contract_Change_Request__c ccr:newRequests){
            if(ccr.RecordTypeId == Schema.SObjectType.Contract_Change_Request__c.getRecordTypeInfosByDeveloperName().get('AccountIDChange').getRecordTypeId()){
                if(!contractIdSet.contains(ccr.Contract__c)){
                    ccr.addError('仅MSP转售的合同可以创建客户账号ID调整');
                }
            }
        }

        for(Contract_Product__c cp:[SELECT id,Contract__c,CustomerIDs__c FROM Contract_Product__c WHERE Contract__c in:contractIdToccrMap.keySet() and shouldhaveCustomerIDs__c = true]){//MSP转售云产品
            if(!contractIdToccrMap.containsKey(cp.Contract__c))continue;
            ContractChangeRequestDetail__c ccrDetail = new ContractChangeRequestDetail__c();
            ccrDetail.ContarctProduct__c = cp.id;
            ccrDetail.ContractChangeRequest__c = contractIdToccrMap.get(cp.Contract__c);
            ccrDetail.CustomerAccountIDs__c = cp.CustomerIDs__c;
            insertList.add(ccrDetail);
        }

        if(insertList.size()>0) insert insertList;
    }


}