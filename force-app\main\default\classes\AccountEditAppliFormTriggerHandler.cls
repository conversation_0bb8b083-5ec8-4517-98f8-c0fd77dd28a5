public with sharing class AccountEditAppliFormTriggerHandler extends TriggerHandler {
    public AccountEditAppliFormTriggerHandler() {
        super('AccountEditAppliForm__c');  

    }

   public override void doAfterUpdate(List<SObject> newList, Map<Id, SObject> oldMap) {
        // 处理客户修改申请表审批通过后的 字段更新 和 SAP同步
        handleAccountEditAppliFormApproval((List<AccountEditAppliForm__c>)newList,
        (Map<Id, AccountEditAppliForm__c>)oldMap);

   }

  // ================== override方法结束，自定义方法开始 ==================

   private void handleAccountEditAppliFormApproval(List<AccountEditAppliForm__c> newForms, Map<Id, AccountEditAppliForm__c> oldForms) {
        System.debug('=== handleAccountEditAppliFormApproval 方法开始 ===');
        System.debug('输入参数 - newForms数量: ' + newForms.size());
        System.debug('输入参数 - oldForms数量: ' + oldForms.size());

        Set<Id> accountIdsToProcess = new Set<Id>();
        Map<Id, AccountEditAppliForm__c> approvedForms = new Map<Id, AccountEditAppliForm__c>();

        // 只处理审批通过的申请表
        for(AccountEditAppliForm__c newForm : newForms) {
            AccountEditAppliForm__c oldForm = oldForms.get(newForm.Id);
            System.debug('检查申请表 - ID: ' + newForm.Id + ', 旧状态: ' + oldForm.ApprovalStatus__c + ', 新状态: ' + newForm.ApprovalStatus__c + ', 关联客户: ' + newForm.Account__c);

            if(newForm.ApprovalStatus__c != oldForm.ApprovalStatus__c && newForm.ApprovalStatus__c == 'Approved' && newForm.Account__c != null) {
                System.debug('申请表审批通过，添加到处理列表 - 申请表ID: ' + newForm.Id + ', 客户ID: ' + newForm.Account__c);
                accountIdsToProcess.add(newForm.Account__c);
                approvedForms.put(newForm.Id, newForm);
            } else {
                System.debug('申请表不满足处理条件 - 状态变化: ' + (newForm.ApprovalStatus__c != oldForm.ApprovalStatus__c) + ', 是否已审批: ' + (newForm.ApprovalStatus__c == 'Approved') + ', 是否有关联客户: ' + (newForm.Account__c != null));
            }
        }

        System.debug('筛选结果 - 需要处理的客户数量: ' + accountIdsToProcess.size());
        System.debug('筛选结果 - 审批通过的申请表数量: ' + approvedForms.size());
        System.debug('筛选结果 - 客户ID列表: ' + accountIdsToProcess);

        if(accountIdsToProcess.isEmpty()) {
            System.debug('没有需要处理的申请表，方法结束');
            return;
        }

        // 更新客户字段
        System.debug('开始更新客户字段...');
        updateAccountFields(approvedForms, accountIdsToProcess);
        System.debug('客户字段更新完成');

        // 查询Account的SAP编号用于SAP同步
        System.debug('开始查询客户SAP信息用于同步...');
        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, SAP_Num__c,
            (SELECT Id, Product_Line__c, Sales_Status__c FROM ChildAccounts__r limit 1)
                FROM Account
                WHERE Id IN :accountIdsToProcess
        ]);
        System.debug('查询到的客户数量: ' + accountMap.size());
        System.debug('查询到的客户详情: ' + accountMap);

        Map<Id, String> createAccountIdMap = new Map<Id, String>();

        System.debug('开始构建SAP同步参数...');
        for(AccountEditAppliForm__c form : approvedForms.values()) {
            Account acc = accountMap.get(form.Account__c);
            System.debug('处理申请表: ' + form.Id + ', 关联客户: ' + form.Account__c);

            if(acc != null) {
                String ProductLine = (acc.ChildAccounts__r<>null && acc.ChildAccounts__r.size()>0)?acc.ChildAccounts__r[0].Product_Line__c:'P1';
                System.debug('客户信息 - ID: ' + acc.Id + ', SAP编号: ' + acc.SAP_Num__c + ', 产品线: ' + ProductLine);
                System.debug('销售状态记录数量: ' + (acc.ChildAccounts__r != null ? acc.ChildAccounts__r.size() : 0));

                createAccountIdMap.put(acc.Id, ProductLine);
                System.debug('已添加到SAP同步列表 - 客户ID: ' + acc.Id + ', 产品线: ' + ProductLine);
            } else {
                System.debug('未找到对应的客户记录 - 客户ID: ' + form.Account__c);
            }
        }

        System.debug('SAP同步参数构建完成 - createAccountIdMap数量: ' + createAccountIdMap.size());
        System.debug('SAP同步参数详情: ' + createAccountIdMap);

        if(createAccountIdMap.size() > 0) {
            System.debug('开始调用SAP同步接口...');
            try {
                Interface_CRMSyncAccountToSAP.doSyncAccountToSAP(createAccountIdMap);
                System.debug('SAP同步接口调用成功');
            } catch(Exception e) {
                System.debug('SAP同步接口调用失败: ' + e.getMessage());
                System.debug('错误堆栈: ' + e.getStackTraceString());
            }
        } else {
            System.debug('没有需要同步到SAP的客户');
        }

        System.debug('=== handleAccountEditAppliFormApproval 方法结束 ===');
    }

    /**
     * 更新客户字段
     * 将审批通过的客户修改申请表字段值更新到对应的客户记录上
     */
    private void updateAccountFields(Map<Id, AccountEditAppliForm__c> approvedForms, Set<Id> accountIdsToProcess) {
        System.debug('=== updateAccountFields 方法开始 ===');
        System.debug('审批通过的申请表数量: ' + approvedForms.size());
        System.debug('需要更新的客户ID: ' + accountIdsToProcess);

        // // 查询需要更新的客户记录
        // Map<Id, Account> accountsToUpdate = new Map<Id, Account>([
        //     SELECT Id, ParentId, Name, Customer_SimpleName__c, X1__c, SAP_Num__c, Phone,
        //            Customer_Category__c, Customer_Industry__c, CompanySize__c, Payment_Terms__c,
        //            Customer_Domain__c, Customer_Location_Region__c, Risk_Attribute__c,
        //            Legal_Representative__c, Business_License_Number__c, Customer_ICP_Filing_Number__c,
        //            Customer_ICP_Number__c, Customer_Invoice_Header__c, Is_Singapore_Account__c,
        //            Tax_Categroy__c, TAX_Num1__c, CountryId__c, ProvinceId__c, City__c,
        //            Street__c, PostalCode__c
        //     FROM Account
        //     WHERE Id IN :accountIdsToProcess
        // ]);

        List<Account> accountsToUpdateList = new List<Account>();

        // 遍历审批通过的申请表，更新对应的客户字段
        for(AccountEditAppliForm__c form : approvedForms.values()) {
            Account accountToUpdate = new Account();
            accountToUpdate.Id = form.Account__c;
            if(String.isNotEmpty(form.ParentId__c)) {
                accountToUpdate.ParentId = form.ParentId__c;
            }
                // 映射字段值从申请表到客户
                
                // Name字段是必填的，确保不为空
                // if(String.isNotBlank(form.Account__r.Name)) {
                //     accountToUpdate.Name = form.Account__r.Name;
                // } else {
                //     System.debug('警告：Customer_Name__c为空，保持原有Name值');
                // }

                if(String.isNotEmpty(form.AccountName__c)) {
                    accountToUpdate.Name = form.AccountName__c;
                }
                if(String.isNotEmpty(form.Customer_SimpleName__c)){
                    accountToUpdate.Customer_SimpleName__c = form.Customer_SimpleName__c;
                }
                if(String.isNotEmpty(form.X1__c))
                accountToUpdate.X1__c = form.X1__c;
                if(String.isNotEmpty(form.SAP_Num__c))
                accountToUpdate.SAP_Num__c = form.SAP_Num__c;
                if(String.isNotEmpty(form.phone__c))
                accountToUpdate.Phone = form.phone__c;
                if(String.isNotEmpty(form.Customer_Category__c))
                accountToUpdate.Customer_Category__c = form.Customer_Category__c;
                if(String.isNotEmpty(form.Customer_Industry_Category__c))
                accountToUpdate.Customer_Industry__c = form.Customer_Industry_Category__c;
                if(String.isNotEmpty(form.CompanySize__c))
                accountToUpdate.CompanySize__c = form.CompanySize__c;
                if(String.isNotEmpty(form.Payment_Terms__c))
                accountToUpdate.Payment_Terms__c = form.Payment_Terms__c;
                if(String.isNotEmpty(form.Customer_Domain__c))
                accountToUpdate.Customer_Domain__c = form.Customer_Domain__c;
                if(String.isNotEmpty(form.Customer_Location_Region__c))
                accountToUpdate.Customer_Location_Region__c = form.Customer_Location_Region__c;
                if(String.isNotEmpty(form.Risk_Attribute__c))
                accountToUpdate.Risk_Attribute__c = form.Risk_Attribute__c;
                if(String.isNotEmpty(form.Legal_Representative__c))
                accountToUpdate.Legal_Representative__c = form.Legal_Representative__c;
                if(String.isNotEmpty(form.Business_License_Number__c))
                accountToUpdate.Business_License_Number__c = form.Business_License_Number__c;
                if(String.isNotEmpty(form.Customer_ICP_Filing_Number__c))
                accountToUpdate.Customer_ICP_Filing_Number__c = form.Customer_ICP_Filing_Number__c;
                if(String.isNotEmpty(form.Customer_ICP_Number__c))
                accountToUpdate.Customer_ICP_Number__c = form.Customer_ICP_Number__c;
                if(String.isNotEmpty(form.Customer_Invoice_Header__c))
                accountToUpdate.Customer_Invoice_Header__c = form.Customer_Invoice_Header__c;
                
                accountToUpdate.Is_Singapore_Account__c = form.Is_Singapore_Account__c;
                if(String.isNotEmpty(form.Tax_Categroy__c))
                accountToUpdate.Tax_Categroy__c = form.Tax_Categroy__c;
                if(String.isNotEmpty(form.VAT_Registration_Number__c))
                accountToUpdate.TAX_Num1__c = form.VAT_Registration_Number__c;
                if(String.isNotEmpty(form.Country__c))
                accountToUpdate.CountryId__c = form.Country__c;
                if(String.isNotEmpty(form.Province__c))
                accountToUpdate.ProvinceId__c = form.Province__c;
                if(String.isNotEmpty(form.City__c))
                accountToUpdate.City__c = form.City__c;
                if(String.isNotEmpty(form.Street__c))
                accountToUpdate.Street__c = form.Street__c;
                if(String.isNotEmpty(form.Postal_Code__c))
                accountToUpdate.PostalCode__c = form.Postal_Code__c;

                accountsToUpdateList.add(accountToUpdate);

                System.debug('客户字段更新完成: ' + accountToUpdate.Id);
            
        }

        // 批量更新客户记录
        if(!accountsToUpdateList.isEmpty()) {
            System.debug('开始批量更新客户记录，数量: ' + accountsToUpdateList.size());
            try {
                update accountsToUpdateList;
                System.debug('客户记录更新成功');
            } catch(Exception e) {
                System.debug('客户记录更新失败: ' + e.getMessage());
                System.debug('错误堆栈: ' + e.getStackTraceString());
            }
        } else {
            System.debug('没有需要更新的客户记录');
        }

        System.debug('=== updateAccountFields 方法结束 ===');
    }
}